@extends('layouts.dashboard')

@section('title', 'Application Details - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/applications')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/applications')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <div class="flex items-center mb-2">
                                <a href="{{ route('student.applications') }}" class="mr-4 text-green-100 hover:text-white transition-colors duration-200">
                                    <i class="fas fa-arrow-left text-lg"></i>
                                </a>
                                <h1 class="text-2xl lg:text-3xl font-bold">Application Details</h1>
                            </div>
                            <p class="text-green-100 text-sm lg:text-base">View complete details of your scholarship application</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <span class="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg">
                                <i class="fas fa-file-alt mr-2"></i>
                                ID: {{ $application->application_id }}
                            </span>
                            <a href="{{ route('student.student.download.application', $application->id) }}" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-200">
                                <i class="fas fa-download mr-2"></i>
                                Download Application
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Details -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <!-- Application Status Card -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div class="mb-4 lg:mb-0">
                                <h2 class="text-xl font-semibold text-gray-900 mb-2">
                                    {{ $application->scholarship->title ?? 'University Scholarship' }}
                                </h2>
                                <p class="text-gray-600">
                                    Applied on {{ $application->created_at->format('M d, Y') }} at {{ $application->created_at->format('g:i A') }}
                                </p>
                            </div>
                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium
                                    @if($application->status === 'approved') bg-green-100 text-green-800
                                    @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                    @elseif($application->status === 'under_review') bg-orange-100 text-orange-800
                                    @else bg-yellow-100 text-yellow-800
                                    @endif">
                                    <i class="fas {{ $application->status === 'approved' ? 'fa-check-circle' : ($application->status === 'rejected' ? 'fa-times-circle' : ($application->status === 'under_review' ? 'fa-search' : 'fa-clock')) }} mr-2"></i>
                                    {{ ucfirst(str_replace('_', ' ', $application->status)) }}
                                </span>
                                @if($application->scholarship)
                                    <span class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                        <i class="fas fa-money-bill-wave mr-2"></i>
                                        ₦{{ number_format($application->scholarship->amount) }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        @if($application->status === 'approved')
                            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                                </div>
                            </div>
                        @elseif($application->status === 'rejected' && $application->review_notes)
                            <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-red-600 mr-2 mt-0.5"></i>
                                    <div>
                                        <span class="text-red-800 font-medium block">Application not approved</span>
                                        <p class="text-red-700 text-sm mt-1">{{ $application->review_notes }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Application Information Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- Personal Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-user text-green-600 mr-2"></i>
                                Personal Information
                            </h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Full Name</label>
                                    <p class="text-gray-900">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Email</label>
                                    <p class="text-gray-900">{{ Auth::user()->email }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Phone Number</label>
                                    <p class="text-gray-900">{{ Auth::user()->phone_number ?? 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Matriculation Number</label>
                                    <p class="text-gray-900">{{ $application->matriculation_number ?? 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-graduation-cap text-green-600 mr-2"></i>
                                Academic Information
                            </h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="text-sm font-medium text-gray-600">University</label>
                                    <p class="text-gray-900">{{ Auth::user()->university_name ?? 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Course of Study</label>
                                    <p class="text-gray-900">{{ $application->course_of_study ?? 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Current Level</label>
                                    <p class="text-gray-900">{{ Auth::user()->current_level ?? 'Not provided' }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-600">Year of Study</label>
                                    <p class="text-gray-900">{{ Auth::user()->year_of_study ?? 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Application Responses -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-edit text-green-600 mr-2"></i>
                            Application Responses
                        </h3>
                        <div class="space-y-6">
                            @if($application->form_data)
                                @if(isset($application->form_data['personal_statement']))
                                    <div>
                                        <label class="text-sm font-medium text-gray-600 block mb-2">Personal Statement</label>
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <p class="text-gray-900 whitespace-pre-wrap">{{ $application->form_data['personal_statement'] }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if(isset($application->form_data['financial_need']))
                                    <div>
                                        <label class="text-sm font-medium text-gray-600 block mb-2">Financial Need Statement</label>
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <p class="text-gray-900 whitespace-pre-wrap">{{ $application->form_data['financial_need'] }}</p>
                                        </div>
                                    </div>
                                @endif
                            @else
                                <p class="text-gray-500 italic">No application responses available.</p>
                            @endif
                        </div>
                    </div>

                    <!-- Submitted Documents -->
                    @if($application->files->count() > 0)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                Submitted Documents
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($application->files as $file)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center">
                                                <i class="fas fa-file text-gray-400 mr-2"></i>
                                                <span class="text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $file->field_name)) }}</span>
                                            </div>
                                            <span class="text-xs text-gray-500">{{ $file->getFileSizeHumanAttribute() }}</span>
                                        </div>
                                        <p class="text-xs text-gray-600 mb-3 truncate">{{ $file->original_name }}</p>
                                        <div class="flex gap-2">
                                            <a href="{{ route('student.download.document', $file->id) }}" 
                                               class="inline-flex items-center px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded text-xs transition-colors duration-200">
                                                <i class="fas fa-download mr-1"></i>
                                                Download
                                            </a>
                                            @if($file->isImage())
                                                <button onclick="previewImage('{{ $file->getFileUrlAttribute() }}', '{{ $file->original_name }}')" 
                                                        class="inline-flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs transition-colors duration-200">
                                                    <i class="fas fa-eye mr-1"></i>
                                                    Preview
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Application Timeline -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-clock text-green-600 mr-2"></i>
                            Application Timeline
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-check text-white text-sm"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Application Submitted</p>
                                    <p class="text-sm text-gray-600">{{ $application->created_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                            
                            @if($application->reviewed_at)
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-search text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">Application Reviewed</p>
                                        <p class="text-sm text-gray-600">{{ $application->reviewed_at->format('M d, Y \a\t g:i A') }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="imagePreviewModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900" id="imagePreviewTitle">Document Preview</h3>
                    <button onclick="closeImagePreview()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <img id="imagePreviewContent" src="" alt="Document Preview" class="w-full h-auto max-h-96 object-contain">
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function previewImage(imageUrl, fileName) {
    document.getElementById('imagePreviewContent').src = imageUrl;
    document.getElementById('imagePreviewTitle').textContent = fileName;
    document.getElementById('imagePreviewModal').classList.remove('hidden');
}

function closeImagePreview() {
    document.getElementById('imagePreviewModal').classList.add('hidden');
}
</script>
@endsection 