{{-- Dashboard Sidebar Component --}}
@props([
    'userType' => 'user',
    'menuItems' => []
])

<div class="bg-white border-r border-gray-200 h-full">
    <!-- User Profile Section -->
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-700 font-medium text-sm">
                    {{ strtoupper(substr(Auth::user()->first_name, 0, 1)) }}{{ strtoupper(substr(Auth::user()->last_name, 0, 1)) }}
                </span>
            </div>
            <div class="ml-3">
                <h3 class="font-medium text-gray-900 text-sm">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</h3>
                <p class="text-xs text-gray-600 capitalize">{{ str_replace('_', ' ', Auth::user()->role) }}</p>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="p-3">
        <ul class="space-y-1">
            @foreach($menuItems as $item)
                <li>
                    <a href="{{ $item['url'] }}"
                       @if(isset($item['external']) && $item['external']) target="_blank" rel="noopener noreferrer" @endif
                       class="flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50 transition-colors {{ (isset($item['active']) && $item['active']) || (!isset($item['external']) && request()->is(trim($item['url'], '/'))) ? 'bg-blue-50 text-blue-700' : '' }}">
                        <i class="fas fa-{{ $item['icon'] }} mr-3 text-gray-500 {{ (isset($item['active']) && $item['active']) || (!isset($item['external']) && request()->is(trim($item['url'], '/'))) ? 'text-blue-600' : '' }}"></i>
                        <span class="font-medium">{{ $item['label'] }}</span>
                        @if(isset($item['external']) && $item['external'])
                            <i class="fas fa-external-link-alt ml-auto text-xs text-gray-400"></i>
                        @endif
                        @if(isset($item['badge']))
                            <span class="ml-auto px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                {{ $item['badge'] }}
                            </span>
                        @endif
                    </a>
                </li>
            @endforeach
        </ul>
    </nav>

    <!-- Quick Actions -->
    <div class="p-3 border-t border-gray-200 mt-auto">
        <div class="space-y-1">
            @if(Auth::user()->role === 'admin')
                <a href="{{ route('settings.index') ?? '#' }}" 
                   class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-cog mr-3 text-gray-500"></i>
                    Settings
                </a>
            @endif
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit" 
                        class="flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-sign-out-alt mr-3 text-gray-500"></i>
                    Logout
                </button>
            </form>
        </div>
    </div>
</div>
