<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Details - {{ $student->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        .student-photo {
            text-align: center;
            margin: 20px 0;
        }
        .student-photo img {
            max-width: 150px;
            max-height: 150px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            font-size: 18px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            margin-top: 5px;
        }
        .applications-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .applications-table th,
        .applications-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .applications-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-approved { background-color: #d4edda; color: #155724; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-rejected { background-color: #f8d7da; color: #721c24; }
        .status-under-review { background-color: #cce5ff; color: #004085; }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Student Details Report</h1>
        <p>Generated on {{ now()->format('F d, Y \a\t g:i A') }}</p>
    </div>

    <!-- Student Photo -->
    @if($student->photo)
    <div class="student-photo">
        <img src="{{ storage_path('app/public/' . $student->photo) }}" alt="Student Photo">
    </div>
    @endif

    <!-- Basic Information -->
    <div class="section">
        <h2>Basic Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Full Name:</div>
                <div class="info-value">{{ $student->name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Student ID:</div>
                <div class="info-value">#{{ $student->id }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Class/Level:</div>
                <div class="info-value">{{ $student->class ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Gender:</div>
                <div class="info-value">{{ ucfirst($student->gender ?? 'N/A') }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Age:</div>
                <div class="info-value">{{ $student->age ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div class="info-value">{{ ucfirst($student->status ?? 'N/A') }}</div>
            </div>
        </div>
    </div>

    <!-- Parent/Guardian Information -->
    <div class="section">
        <h2>Parent/Guardian Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Parent Name:</div>
                <div class="info-value">{{ $student->parent_name ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Parent Phone:</div>
                <div class="info-value">{{ $student->parent_phone ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Address:</div>
                <div class="info-value">{{ $student->address ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Emergency Contact:</div>
                <div class="info-value">{{ $student->emergency_contact ?? 'N/A' }}</div>
            </div>
        </div>
    </div>

    <!-- Academic Information -->
    <div class="section">
        <h2>Academic Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Academic Performance:</div>
                <div class="info-value">{{ $student->academic_performance ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Previous School:</div>
                <div class="info-value">{{ $student->previous_school ?? 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Enrollment Date:</div>
                <div class="info-value">{{ $student->enrollment_date ? $student->enrollment_date->format('M d, Y') : 'N/A' }}</div>
            </div>
        </div>
    </div>

    <!-- Scholarship Applications -->
    <div class="section">
        <h2>Scholarship Applications ({{ $applications->count() }})</h2>
        @if($applications->count() > 0)
        <table class="applications-table">
            <thead>
                <tr>
                    <th>Application ID</th>
                    <th>Scholarship ID</th>
                    <th>Scholarship Title</th>
                    <th>Status</th>
                    <th>Applied Date</th>
                    <th>Amount</th>
                    <th>Academic Year</th>
                </tr>
            </thead>
            <tbody>
                @foreach($applications as $application)
                <tr>
                    <td><strong>#{{ $application->id }}</strong></td>
                    <td><strong>#{{ $application->scholarship->id ?? 'N/A' }}</strong></td>
                    <td>{{ $application->scholarship->title ?? 'N/A' }}</td>
                    <td>
                        <span class="status-badge status-{{ $application->status }}">
                            {{ ucfirst($application->status) }}
                        </span>
                    </td>
                    <td>{{ $application->created_at->format('M d, Y') }}</td>
                    <td>
                        @if($application->scholarship && $application->scholarship->amount)
                            ₦{{ number_format($application->scholarship->amount) }}
                        @else
                            N/A
                        @endif
                    </td>
                    <td>{{ $application->academic_year ?? 'N/A' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @else
        <p>No scholarship applications found for this student.</p>
        @endif
    </div>

    <!-- Additional Information -->
    @if($student->additional_notes || $student->special_needs || $student->medical_conditions)
    <div class="section">
        <h2>Additional Information</h2>
        <div class="info-grid">
            @if($student->additional_notes)
            <div class="info-item">
                <div class="info-label">Additional Notes:</div>
                <div class="info-value">{{ $student->additional_notes }}</div>
            </div>
            @endif
            @if($student->special_needs)
            <div class="info-item">
                <div class="info-label">Special Needs:</div>
                <div class="info-value">{{ $student->special_needs }}</div>
            </div>
            @endif
            @if($student->medical_conditions)
            <div class="info-item">
                <div class="info-label">Medical Conditions:</div>
                <div class="info-value">{{ $student->medical_conditions }}</div>
            </div>
            @endif
        </div>
    </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated automatically by the Scholarship Management System</p>
        <p>For any questions, please contact the administration</p>
    </div>
</body>
</html> 