<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use Illuminate\Http\Request;

class PublicScholarshipController extends Controller
{
    /**
     * Display the specified scholarship.
     */
    public function show($id)
    {
        $scholarship = Scholarship::findOrFail($id);
        
        // Check if scholarship is active and visible
        if (!$scholarship->is_open || $scholarship->status !== 'active') {
            abort(404, 'Scholarship not found or not available.');
        }

        return view('scholarships.show', compact('scholarship'));
    }
} 