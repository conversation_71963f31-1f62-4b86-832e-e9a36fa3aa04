@extends('layouts.dashboard')

@section('title', 'Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student-dashboard')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student-dashboard')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Welcome, {{ Auth::user()->first_name }}!</h1>
                            <p class="text-green-100 text-sm lg:text-base">Manage your scholarship applications and track your progress</p>
                            @if(Auth::user()->university_name)
                                <p class="text-green-200 text-xs lg:text-sm mt-1">{{ Auth::user()->university_name }} - {{ Auth::user()->course_of_study }}</p>
                            @endif
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/applications" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-file-alt mr-2"></i>
                                My Applications
                            </a>
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-user-graduate mr-2"></i>
                                Academic Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning Banner for 100 Level Students -->
            @if(Auth::user()->needsAdmissionLetter())
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mx-6 lg:mx-8 mt-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Admission Letter Required
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>As a 100 level student, you are required to upload your admission letter. This document is essential for scholarship applications and verification purposes.</p>
                                <div class="mt-3">
                                    <a href="/student/profile" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                                        <i class="fas fa-upload mr-2"></i>
                                        Upload Admission Letter
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Available</p>
                                <p class="text-xl font-bold text-green-600">{{ \App\Models\Scholarship::where('status', 'active')->where('category', 'university')->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">My Apps</p>
                                <p class="text-xl font-bold text-blue-600">{{ Auth::user()->scholarshipApplications()->count() }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-chart-line text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Status</p>
                                <p class="text-sm font-bold text-purple-600">{{ ucfirst(str_replace('_', ' ', Auth::user()->scholarshipApplications()->latest()->first()->status ?? 'None')) }}</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                            <div class="text-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-check text-white text-sm"></i>
                                </div>
                                <p class="text-xs font-medium text-gray-600 mb-1">Profile</p>
                                <p class="text-xl font-bold text-indigo-600">{{ Auth::user()->getProfileCompletionPercentage() }}%</p>
                            </div>
                        </div>
                    </div>

                    <!-- Application Status -->
                    @php
                        $activeApplication = Auth::user()->activeScholarshipApplication();
                    @endphp

                    @if($activeApplication)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-alt text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Your Scholarship Application</h2>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-700">{{ $activeApplication->scholarship->title ?? 'University Scholarship' }}</h3>
                                    <p class="text-gray-600 text-sm">Applied on: {{ $activeApplication->created_at->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                        @if($activeApplication->status === 'approved') bg-green-100 text-green-800
                                        @elseif($activeApplication->status === 'rejected') bg-red-100 text-red-800
                                        @elseif($activeApplication->status === 'under_review') bg-yellow-100 text-yellow-800
                                        @else bg-blue-100 text-blue-800
                                        @endif">
                                        <i class="fas {{ $activeApplication->status === 'approved' ? 'fa-check-circle' : ($activeApplication->status === 'rejected' ? 'fa-times-circle' : 'fa-clock') }} mr-1"></i>
                                        {{ ucfirst(str_replace('_', ' ', $activeApplication->status)) }}
                                    </span>
                                </div>
                            </div>

                            @if($activeApplication->status === 'approved')
                                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                        <span class="text-green-800 font-medium">Congratulations! Your application has been approved.</span>
                                    </div>
                                    <div class="mt-2">
                                        <a href="#" class="text-green-600 hover:text-green-800 font-medium text-sm">Download Approval Letter (PDF) →</a>
                                    </div>
                                </div>
                            @elseif($activeApplication->status === 'rejected')
                                <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-times-circle text-red-600 mr-2"></i>
                                        <span class="text-red-800 font-medium">Your application was not approved this time.</span>
                                    </div>
                                    @if($activeApplication->rejection_reason)
                                        <p class="text-red-700 mt-2 text-sm">{{ $activeApplication->rejection_reason }}</p>
                                    @endif
                                </div>
                            @endif
                        </div>
                    @else
                        <!-- No Active Application -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center mb-8">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Active Application</h3>
                            <p class="text-gray-600 mb-6">You haven't applied for any scholarships yet. University students can apply for one scholarship.</p>
                            <a href="{{ frontendUrl('scholarships') }}" target="_blank" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- University Scholarships Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-graduation-cap text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">University Scholarships</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Browse available scholarships for university students.</p>
                            <a href="{{ frontendUrl('scholarships') }}" target="_blank" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-search mr-2"></i>
                                Browse Scholarships
                            </a>
                        </div>

                        <!-- Academic Profile Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-graduate text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">Academic Profile</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Update your academic information and credentials.</p>
                            <a href="/student/profile" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-edit mr-2"></i>
                                Update Profile
                            </a>
                        </div>

                        <!-- Support Center Action -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-life-ring text-white text-sm"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900">Support Center</h3>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Get help and support for your applications.</p>
                            <a href="{{ frontendUrl('contact') }}" target="_blank" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-300 text-sm">
                                <i class="fas fa-headset mr-2"></i>
                                Get Support
                            </a>
                        </div>
                    </div>

                    <!-- Recent Applications Download Section -->
                    @if(Auth::user()->scholarshipApplications->count() > 0)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-download text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Download Application Forms</h2>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Download your submitted scholarship applications with official NGO watermark and status.</p>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach(Auth::user()->scholarshipApplications->take(3) as $application)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-gray-900">{{ $application->scholarship->title ?? 'University Scholarship' }}</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                @if($application->status === 'approved') bg-green-100 text-green-800
                                                @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                                @elseif($application->status === 'under_review') bg-orange-100 text-orange-800
                                                @else bg-yellow-100 text-yellow-800
                                                @endif">
                                                {{ ucfirst(str_replace('_', ' ', $application->status)) }}
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-600 mb-3">Submitted: {{ $application->created_at->format('M d, Y') }}</p>
                                        <a href="{{ route('student.student.download.application', $application->id) }}" class="inline-flex items-center px-3 py-1 bg-orange-100 hover:bg-orange-200 text-orange-700 rounded text-xs transition-colors duration-200">
                                            <i class="fas fa-download mr-1"></i>
                                            Download
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                            @if(Auth::user()->scholarshipApplications->count() > 3)
                                <div class="mt-4 text-center">
                                    <a href="/student/applications" class="text-orange-600 hover:text-orange-700 font-medium text-sm">View All Applications →</a>
                                </div>
                            @endif
                        </div>
                    @endif

                    <!-- Student Information and Resources -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Academic Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-university text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Academic Information</h2>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">University:</span>
                                    <span class="font-medium text-gray-900 text-sm">{{ Auth::user()->university_name ?? 'Not specified' }}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Course of Study:</span>
                                    <span class="font-medium text-gray-900 text-sm">{{ Auth::user()->course_of_study ?? 'Not specified' }}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Year of Study:</span>
                                    <span class="font-medium text-gray-900 text-sm">{{ Auth::user()->year_of_study ?? 'Not specified' }}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600 text-sm">Student ID:</span>
                                    <span class="font-medium text-gray-900 text-sm">{{ Auth::user()->student_id ?? 'Not specified' }}</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <a href="/student/profile" class="text-green-600 hover:text-green-700 font-medium text-sm">Update Information →</a>
                            </div>
                        </div>

                        <!-- Application Guidelines -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-info-circle text-white text-sm"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-900">Application Guidelines</h2>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">1</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">University students can apply for one scholarship at a time.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">2</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">Ensure all required documents are uploaded before submission.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">3</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">You can track your application status from this dashboard.</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-green-600 text-xs font-bold">4</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">Approved applications will generate a PDF approval letter.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Applications -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                                <h2 class="text-lg font-semibold text-gray-900">Recent Applications</h2>
                            </div>
                            <a href="/student/applications" class="text-green-600 hover:text-green-700 font-medium text-sm">View All →</a>
                        </div>
                        @php
                            $recentApplications = Auth::user()->scholarshipApplications()->with('scholarship')->latest()->limit(5)->get();
                        @endphp

                        @if($recentApplications->count() > 0)
                            <div class="space-y-3">
                                @foreach($recentApplications as $application)
                                    <div class="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow duration-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <h3 class="font-medium text-gray-900 text-sm">{{ $application->scholarship->title ?? 'N/A' }}</h3>
                                                <p class="text-xs text-gray-600 mt-1">{{ ucfirst($application->scholarship->category ?? 'N/A') }} • Applied {{ $application->submitted_at ? $application->submitted_at->format('M j, Y') : 'N/A' }}</p>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                @if($application->status === 'approved')
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        Approved
                                                    </span>
                                                @elseif($application->status === 'rejected')
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-times-circle mr-1"></i>
                                                        Rejected
                                                    </span>
                                                @elseif($application->status === 'pending')
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Pending
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <i class="fas fa-question-circle mr-1"></i>
                                                        {{ ucfirst($application->status) }}
                                                    </span>
                                                @endif
                                                @if($application->status === 'approved')
                                                    <a href="/api/v1/scholarship-applications/{{ $application->id }}/pdf"
                                                       class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
                                                       target="_blank">
                                                        <i class="fas fa-download mr-1"></i> PDF
                                                    </a>
                                                @endif
                                                <a href="/student/applications/{{ $application->id }}" class="text-gray-400 hover:text-gray-600">
                                                    <i class="fas fa-chevron-right text-xs"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-700 mb-2">No Applications Yet</h3>
                                <p class="text-gray-600 mb-6">You haven't submitted any applications yet.</p>
                                <a href="/scholarships" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-graduation-cap mr-2"></i>
                                    Browse Scholarships
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Profile Completion Warning -->
                    @if(Auth::user()->getProfileCompletionPercentage() < 100)
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">
                                        Complete Your Profile
                                    </h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <p>Your profile is {{ Auth::user()->getProfileCompletionPercentage() }}% complete. Please update your profile with all required information to improve your scholarship application chances.</p>
                                        <div class="mt-3">
                                            <a href="/student/profile" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-800 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                                <i class="fas fa-user-edit mr-2"></i>
                                                Complete Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Mobile Menu Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                const mobileSidebar = document.getElementById('mobile-sidebar');
                const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
                const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

                function openMobileMenu() {
                    mobileSidebar.classList.remove('hidden');
                    setTimeout(() => {
                        mobileSidebarContent.classList.remove('-translate-x-full');
                    }, 10);
                }

                function closeMobileMenu() {
                    mobileSidebarContent.classList.add('-translate-x-full');
                    setTimeout(() => {
                        mobileSidebar.classList.add('hidden');
                    }, 300);
                }

                mobileMenuBtn?.addEventListener('click', openMobileMenu);
                mobileSidebarOverlay?.addEventListener('click', closeMobileMenu);
            });
        </script>
    </div>
</div>
@endsection
