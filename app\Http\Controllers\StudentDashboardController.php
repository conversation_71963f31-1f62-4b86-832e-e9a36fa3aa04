<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\User;
use App\Models\EducationalResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;

class StudentDashboardController extends Controller
{
    /**
     * Display the main student dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Ensure user is a university student
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Get dashboard statistics
        $stats = [
            'available_scholarships' => Scholarship::where('status', 'open')
                ->where('is_open', true)
                ->where('category', 'university')
                ->where('application_deadline', '>', now())
                ->count(),
            'my_applications' => $user->scholarshipApplications()->count(),
            'pending_applications' => $user->scholarshipApplications()
                ->where('status', 'pending')
                ->count(),
            'approved_applications' => $user->scholarshipApplications()
                ->where('status', 'approved')
                ->count(),
        ];

        // Get recent application
        $recentApplication = $user->scholarshipApplications()
            ->with('scholarship')
            ->latest()
            ->first();

        // Get available scholarships (limited for dashboard)
        $availableScholarships = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->where('category', 'university')
            ->where('application_deadline', '>', now())
            ->orderBy('application_deadline', 'asc')
            ->take(3)
            ->get();

        return view('dashboards.student.index', compact('stats', 'recentApplication', 'availableScholarships'));
    }

    /**
     * Display My Applications section
     */
    public function applications()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $applications = $user->scholarshipApplications()
            ->with(['scholarship', 'files'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $stats = [
            'total' => $user->scholarshipApplications()->count(),
            'pending' => $user->scholarshipApplications()->where('status', 'pending')->count(),
            'under_review' => $user->scholarshipApplications()->where('status', 'under_review')->count(),
            'approved' => $user->scholarshipApplications()->where('status', 'approved')->count(),
            'rejected' => $user->scholarshipApplications()->where('status', 'rejected')->count(),
        ];

        return view('dashboards.student.applications', compact('applications', 'stats'));
    }

    /**
     * Display Available Scholarships section
     */
    public function scholarships(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $query = Scholarship::where('status', 'open')
            ->where('is_open', true)
            ->where('category', 'university')
            ->where('application_deadline', '>', now());

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply amount filter
        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->get('min_amount'));
        }
        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->get('max_amount'));
        }

        $scholarships = $query->orderBy('application_deadline', 'asc')->paginate(12);

        // Check which scholarships user has already applied for
        $appliedScholarshipIds = $user->scholarshipApplications()
            ->pluck('scholarship_id')
            ->toArray();

        return view('dashboards.student.scholarships', compact('scholarships', 'appliedScholarshipIds'));
    }

    /**
     * Display Academic Profile section
     */
    public function profile()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        return view('dashboards.student.profile', compact('user'));
    }

    /**
     * Update Academic Profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone_number' => 'nullable|string|max:20',
            'university_name' => 'required|string|max:255',
            'matriculation_number' => 'required|string|max:50|unique:users,matriculation_number,' . $user->id,
            'course_of_study' => 'required|string|max:255',
            'current_level' => 'required|string|max:50',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'admission_letter' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $validator->validated();

        // Handle admission letter upload
        if ($request->hasFile('admission_letter')) {
            $file = $request->file('admission_letter');
            $filename = 'admission_letter_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('admission_letters', $filename, 'public');
            $data['admission_letter'] = $path;
        }

        $user->update($data);

        return redirect()->route('student.profile')
            ->with('success', 'Profile updated successfully!');
    }



    /**
     * Display scholarship application form
     */
    public function applyForScholarship($scholarshipId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $scholarship = Scholarship::findOrFail($scholarshipId);
        
        // Check if scholarship is still open
        if (!$scholarship->is_open || $scholarship->status !== 'open' || $scholarship->application_deadline < now()) {
            return redirect()->route('student.scholarships')
                ->with('error', 'This scholarship is no longer accepting applications.');
        }

        // Check if user has already applied
        $existingApplication = $user->scholarshipApplications()
            ->where('scholarship_id', $scholarshipId)
            ->first();

        if ($existingApplication) {
            return redirect()->route('student.applications')
                ->with('error', 'You have already applied for this scholarship.');
        }

        return view('dashboards.student.apply-scholarship', compact('scholarship', 'user'));
    }

    /**
     * Submit scholarship application
     */
    public function submitScholarshipApplication(Request $request, $scholarshipId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $scholarship = Scholarship::findOrFail($scholarshipId);
        
        // Check if scholarship is still open
        if (!$scholarship->is_open || $scholarship->status !== 'open' || $scholarship->application_deadline < now()) {
            return redirect()->route('student.scholarships')
                ->with('error', 'This scholarship is no longer accepting applications.');
        }

        // Check if user has already applied
        $existingApplication = $user->scholarshipApplications()
            ->where('scholarship_id', $scholarshipId)
            ->first();

        if ($existingApplication) {
            return redirect()->route('student.applications')
                ->with('error', 'You have already applied for this scholarship.');
        }

        // Validate the application data
        $validator = Validator::make($request->all(), [
            'personal_statement' => 'required|string|min:100|max:1000',
            'academic_achievements' => 'required|string|min:50|max:500',
            'financial_need' => 'required|string|min:50|max:500',
            'future_goals' => 'required|string|min:50|max:500',
            'academic_transcript' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'passport_photo' => 'required|file|mimes:jpg,jpeg,png|max:1024',
            'id_card' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create the application
        $application = $user->scholarshipApplications()->create([
            'scholarship_id' => $scholarshipId,
            'status' => 'pending',
            'matriculation_number' => $user->matriculation_number,
            'course_of_study' => $user->course_of_study,
            'form_data' => [
                'personal_statement' => $request->personal_statement,
                'academic_achievements' => $request->academic_achievements,
                'financial_need' => $request->financial_need,
                'future_goals' => $request->future_goals,
                'university_name' => $user->university_name,
                'current_level' => $user->current_level,
            ],
            'application_data' => [
                'personal_statement' => $request->personal_statement,
                'academic_achievements' => $request->academic_achievements,
                'financial_need' => $request->financial_need,
                'future_goals' => $request->future_goals,
                'university_name' => $user->university_name,
                'current_level' => $user->current_level,
            ],
        ]);

        // Handle file uploads
        $files = ['academic_transcript', 'passport_photo', 'id_card'];
        foreach ($files as $fileField) {
            if ($request->hasFile($fileField)) {
                $file = $request->file($fileField);
                $filename = $fileField . '_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('scholarship_applications/' . $application->id, $filename, 'public');
                
                // Store file information
                $application->files()->create([
                    'field_name' => $fileField,
                    'file_path' => $path,
                    'original_name' => $file->getClientOriginalName(),
                    'stored_name' => $filename,
                    'mime_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                ]);
            }
        }

        return redirect()->route('student.applications')
            ->with('success', 'Your scholarship application has been submitted successfully!');
    }

    /**
     * Display Resources section
     */
    public function resources()
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        $resources = EducationalResource::where('is_active', true)
            ->where(function($query) {
                $query->where('target_audience', 'university')
                      ->orWhere('target_audience', 'all');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('dashboards.student.resources', compact('resources'));
    }

    /**
     * Display Support & Settings section
     */
    public function support()
    {
        $user = Auth::user();

        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Get website settings for support information
        $settings = [
            'support_email' => '<EMAIL>',
            'support_phone' => '+234 ************',
        ];

        return view('dashboards.student.support', compact('user', 'settings'));
    }

    /**
     * View application details
     */
    public function viewApplicationDetails($applicationId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Find the application and verify ownership
        $application = $user->scholarshipApplications()
            ->with(['scholarship', 'files'])
            ->findOrFail($applicationId);

        return view('dashboards.student.application-details', compact('application'));
    }

    /**
     * Download application form as PDF with watermark
     */
    public function downloadApplicationForm($applicationId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Find the application and verify ownership
        $application = $user->scholarshipApplications()
            ->with(['scholarship', 'files'])
            ->findOrFail($applicationId);

        // Generate PDF content
        $pdf = $this->generateApplicationPDF($application, $user);

        // Return PDF response
        return $pdf->download('scholarship_application_' . $application->application_id . '.pdf');
    }

    /**
     * Generate PDF content for application form
     */
    private function generateApplicationPDF($application, $user)
    {
        // Create HTML content for the PDF
        $html = $this->generateApplicationHTML($application, $user);
        
        // Generate PDF using DomPDF
        $pdf = Pdf::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');
        
        return $pdf;
    }

    /**
     * Generate HTML content for application form
     */
    private function generateApplicationHTML($application, $user)
    {
        $statusText = ucfirst(str_replace('_', ' ', $application->status));
        $statusColor = $application->status === 'approved' ? '#059669' : ($application->status === 'rejected' ? '#dc2626' : '#d97706');
        $watermarkText = $application->status === 'approved' ? 'APPROVED' : ($application->status === 'rejected' ? 'REJECTED' : 'UNDER REVIEW');
        
        // Enhanced NGO message based on status
        $message = $application->status === 'approved' 
            ? 'On behalf of HALIMAKQ FOUNDATION, we are delighted to inform you that your scholarship application has been approved. Your dedication to academic excellence and commitment to your educational goals align perfectly with our mission to empower students through education. We believe in your potential and are honored to support your academic journey. This scholarship represents our commitment to fostering educational opportunities and building a brighter future for deserving students.'
            : ($application->status === 'rejected' 
                ? 'Thank you for your interest in our scholarship program and for taking the time to submit your application. After careful consideration by our scholarship committee, we regret to inform you that your application was not approved at this time. We encourage you to continue pursuing your educational goals and to consider applying for future scholarship opportunities. We wish you the very best in your academic endeavors.'
                : 'Thank you for submitting your scholarship application to HALIMAKQ FOUNDATION. Your application is currently under review by our scholarship committee. Our team is carefully evaluating all applications based on academic merit, financial need, and alignment with our mission. We appreciate your patience during this process and will notify you of the decision as soon as possible. We wish you the best of luck.');

        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>HALIMAKQ FOUNDATION - Scholarship Application</title>
            <style>
                @page { margin: 2cm; }
                body { font-family: Arial, sans-serif; margin: 0; line-height: 1.6; font-size: 12px; color: #1f2937; }
                .header { text-align: center; border-bottom: 3px solid #059669; padding-bottom: 20px; margin-bottom: 30px; }
                .logo-section { margin-bottom: 15px; }
                .logo { font-size: 24px; font-weight: bold; color: #059669; margin-bottom: 5px; }
                .logo-subtitle { font-size: 14px; color: #6b7280; font-style: italic; }
                .watermark { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); font-size: 60px; color: rgba(0,0,0,0.08); z-index: 1000; font-weight: bold; }
                .status-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; color: white; font-weight: bold; margin-bottom: 20px; font-size: 12px; }
                .section { margin-bottom: 25px; page-break-inside: avoid; }
                .section-title { font-size: 16px; font-weight: bold; color: #059669; margin-bottom: 12px; border-bottom: 2px solid #e5e7eb; padding-bottom: 5px; }
                .field { margin-bottom: 12px; }
                .field-label { font-weight: bold; color: #374151; margin-bottom: 3px; font-size: 11px; }
                .field-value { background: #f9fafb; padding: 8px; border-radius: 4px; border-left: 3px solid #059669; font-size: 11px; }
                .message-box { background: #f0fdf4; border: 2px solid #059669; border-radius: 8px; padding: 20px; margin: 20px 0; }
                .message-title { font-size: 14px; font-weight: bold; color: #059669; margin-bottom: 10px; }
                .footer { margin-top: 30px; text-align: center; color: #6b7280; font-size: 10px; border-top: 1px solid #e5e7eb; padding-top: 15px; }
                .signature-section { margin-top: 25px; display: flex; justify-content: space-between; }
                .signature-box { width: 45%; text-align: center; }
                .signature-line { border-top: 1px solid #000; margin-top: 40px; }
                .page-break { page-break-before: always; }
                .ngo-info { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 15px; margin: 15px 0; }
                .ngo-info h4 { color: #059669; font-size: 13px; margin-bottom: 8px; }
                .ngo-info p { font-size: 11px; margin: 5px 0; }
            </style>
        </head>
        <body>
            <div class="watermark">' . $watermarkText . '</div>
            
            <div class="header">
                <div class="logo-section">
                    <div class="logo">🎓 HALIMAKQ FOUNDATION</div>
                    <div class="logo-subtitle">Empowering Education Through Scholarships</div>
                </div>
                <h1 style="color: #059669; margin: 10px 0;">Scholarship Application Form</h1>
                <p style="color: #6b7280; font-size: 14px;">Official Application Document</p>
            </div>

            <div class="status-badge" style="background-color: ' . $statusColor . ';">
                Status: ' . $statusText . '
            </div>

            <div class="section">
                <div class="section-title">Application Information</div>
                <div class="field">
                    <div class="field-label">Application ID:</div>
                    <div class="field-value">' . $application->application_id . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Scholarship Program:</div>
                    <div class="field-value">' . ($application->scholarship ? $application->scholarship->title : 'University Merit Scholarship Program') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Application Date:</div>
                    <div class="field-value">' . $application->created_at->format('F d, Y') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Application Time:</div>
                    <div class="field-value">' . $application->created_at->format('g:i A') . '</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Student Personal Information</div>
                <div class="field">
                    <div class="field-label">Full Name:</div>
                    <div class="field-value">' . $user->first_name . ' ' . $user->last_name . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Email Address:</div>
                    <div class="field-value">' . $user->email . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Phone Number:</div>
                    <div class="field-value">' . ($user->phone_number ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Matriculation Number:</div>
                    <div class="field-value">' . ($application->matriculation_number ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Date of Birth:</div>
                    <div class="field-value">' . ($user->date_of_birth ? $user->date_of_birth->format('F d, Y') : 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Gender:</div>
                    <div class="field-value">' . (ucfirst($user->gender ?? 'Not specified')) . '</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Academic Information</div>
                <div class="field">
                    <div class="field-label">University/Institution:</div>
                    <div class="field-value">' . ($user->university_name ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Course of Study:</div>
                    <div class="field-value">' . ($application->course_of_study ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Current Academic Level:</div>
                    <div class="field-value">' . ($user->current_level ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Year of Study:</div>
                    <div class="field-value">' . ($user->year_of_study ?? 'Not provided') . '</div>
                </div>
                <div class="field">
                    <div class="field-label">Student ID:</div>
                    <div class="field-value">' . ($user->student_id ?? 'Not provided') . '</div>
                </div>
            </div>';

        // Add application responses if available
        if ($application->form_data) {
            $html .= '<div class="section">
                <div class="section-title">Application Responses</div>';
            
            if (isset($application->form_data['personal_statement'])) {
                $html .= '<div class="field">
                    <div class="field-label">Personal Statement:</div>
                    <div class="field-value">' . nl2br(htmlspecialchars($application->form_data['personal_statement'])) . '</div>
                </div>';
            }
            
            if (isset($application->form_data['financial_need'])) {
                $html .= '<div class="field">
                    <div class="field-label">Financial Need Statement:</div>
                    <div class="field-value">' . nl2br(htmlspecialchars($application->form_data['financial_need'])) . '</div>
                </div>';
            }
            
            $html .= '</div>';
        }

        // Add submitted documents
        if ($application->files->count() > 0) {
            $html .= '<div class="section">
                <div class="section-title">Submitted Documents</div>';
            
            foreach ($application->files as $file) {
                $html .= '<div class="field">
                    <div class="field-label">' . ucfirst(str_replace('_', ' ', $file->field_name)) . ':</div>
                    <div class="field-value">' . $file->original_name . ' (' . $file->getFileSizeHumanAttribute() . ')</div>
                </div>';
            }
            
            $html .= '</div>';
        }

        $html .= '
            <div class="ngo-info">
                <h4>About HALIMAKQ FOUNDATION</h4>
                <p><strong>Mission:</strong> To empower students through education by providing financial support and opportunities for academic excellence.</p>
                <p><strong>Vision:</strong> A world where every deserving student has access to quality education regardless of financial constraints.</p>
                <p><strong>Core Values:</strong> Excellence, Integrity, Empowerment, and Community Development.</p>
            </div>

            <div class="message-box">
                <div class="message-title">Official Message from HALIMAKQ FOUNDATION</div>
                <p>' . $message . '</p>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <p>Student Signature</p>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <p>NGO Representative</p>
                </div>
            </div>

            <div class="footer">
                <p><strong>Document Generated:</strong> ' . now()->format('F d, Y \a\t g:i A') . '</p>
                <p><strong>HALIMAKQ FOUNDATION</strong> - Empowering Education Through Scholarships</p>
                <p style="font-size: 9px; margin-top: 10px;">This is an official document generated by HALIMAKQ FOUNDATION. For verification, please contact our office.</p>
            </div>
        </body>
        </html>';

        return $html;
    }

    /**
     * Download document
     */
    public function downloadDocument($fileId)
    {
        $user = Auth::user();
        
        if (!$user->isUniversityStudent()) {
            return redirect($user->dashboard_route);
        }

        // Find the file and verify ownership
        $file = \App\Models\ScholarshipApplicationFile::where('id', $fileId)
            ->whereHas('application', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->firstOrFail();

        if (!Storage::exists($file->file_path)) {
            return redirect()->back()->with('error', 'File not found.');
        }

        return Storage::download($file->file_path, $file->original_name);
    }
}
