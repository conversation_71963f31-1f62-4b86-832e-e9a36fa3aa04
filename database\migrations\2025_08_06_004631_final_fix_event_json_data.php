<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Final fix for JSON data in events table
        $events = DB::table('events')->get();
        
        foreach ($events as $event) {
            $updates = [];
            
            // Fix requirements field
            if ($event->requirements) {
                $requirements = $this->extractActualContent($event->requirements);
                if ($requirements !== null) {
                    $updates['requirements'] = json_encode($requirements);
                }
            }
            
            // Fix agenda field
            if ($event->agenda) {
                $agenda = $this->extractActualContent($event->agenda);
                if ($agenda !== null) {
                    $updates['agenda'] = json_encode($agenda);
                }
            }
            
            // Update the event if we have changes
            if (!empty($updates)) {
                DB::table('events')->where('id', $event->id)->update($updates);
            }
        }
    }

    /**
     * Extract actual content from nested JSON structure
     */
    private function extractActualContent($data)
    {
        // First decode
        $decoded = json_decode($data, true);
        
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($decoded)) {
            return [$data]; // Return as single item if not valid JSON
        }
        
        // If the first element is a string that looks like JSON, decode it
        if (count($decoded) > 0 && is_string($decoded[0])) {
            $secondDecode = json_decode($decoded[0], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($secondDecode)) {
                return $secondDecode;
            }
        }
        
        return $decoded;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this migration as it's just fixing data
    }
};
