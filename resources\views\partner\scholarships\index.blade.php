@extends('layouts.dashboard')

@section('title', 'Available Scholarships - Partner Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner/scholarships')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner/scholarships')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Simple Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Scholarships</h1>
                        <p class="text-gray-600 mt-1">Browse available scholarships for your students</p>
                    </div>
                    <div class="flex gap-3">
                        <a href="{{ route('partner.students.index') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                            Manage Students
                        </a>
                        <a href="{{ route('partner.applications.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                            View Applications
                        </a>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="p-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Simple Statistics -->
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-graduation-cap text-gray-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ $stats['total_scholarships'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Open</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ $stats['open_scholarships'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-child text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Primary</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ $stats['primary_scholarships'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-user-graduate text-purple-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Secondary</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ $stats['secondary_scholarships'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-university text-indigo-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">University</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ $stats['university_scholarships'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8">
                        <form method="GET" action="{{ route('partner.scholarships.index') }}" class="flex flex-col lg:flex-row gap-4">
                            <div class="flex-1">
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input type="text"
                                       id="search"
                                       name="search"
                                       value="{{ request('search') }}"
                                       placeholder="Search scholarships..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="lg:w-48">
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select id="category"
                                        name="category"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">All Categories</option>
                                    <option value="primary" {{ request('category') == 'primary' ? 'selected' : '' }}>Primary</option>
                                    <option value="secondary" {{ request('category') == 'secondary' ? 'selected' : '' }}>Secondary</option>
                                    <option value="university" {{ request('category') == 'university' ? 'selected' : '' }}>University</option>
                                </select>
                            </div>
                            <div class="lg:w-48">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="status"
                                        name="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">All Status</option>
                                    <option value="open" {{ request('status') == 'open' ? 'selected' : '' }}>Open</option>
                                    <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>
                            <div class="lg:w-32 flex items-end">
                                <button type="submit" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-300 text-sm">
                                    Search
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Scholarships Grid -->
                    @if($scholarships->count() > 0)
                        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                            @foreach($scholarships as $scholarship)
                                @php
                                    $isOpen = $scholarship->status == 'open' && $scholarship->is_open && $scholarship->application_deadline > now();
                                    $categoryConfig = [
                                        'primary' => ['color' => 'blue', 'icon' => 'fa-child', 'label' => 'Primary School'],
                                        'secondary' => ['color' => 'purple', 'icon' => 'fa-user-graduate', 'label' => 'Secondary School'],
                                        'university' => ['color' => 'indigo', 'icon' => 'fa-university', 'label' => 'University']
                                    ];
                                    $config = $categoryConfig[$scholarship->category] ?? $categoryConfig['primary'];
                                @endphp
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
                                    <div class="p-6">
                                        <!-- Category Badge and Status -->
                                        <div class="flex items-center justify-between mb-4">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-{{ $config['color'] }}-100 text-{{ $config['color'] }}-800">
                                                <i class="fas {{ $config['icon'] }} mr-1"></i>
                                                {{ $config['label'] }}
                                            </span>
                                            <div class="flex items-center space-x-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                    {{ $isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    <i class="fas {{ $isOpen ? 'fa-check-circle' : 'fa-times-circle' }} mr-1"></i>
                                                    {{ $isOpen ? 'Open' : 'Closed' }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Title and Description -->
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $scholarship->title }}</h3>
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ Str::limit($scholarship->description, 120) }}</p>

                                        <!-- Amount and Details -->
                                        <div class="space-y-3 mb-4">
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Amount:</span>
                                                <span class="text-lg font-bold text-green-600">₦{{ number_format($scholarship->amount, 0) }}</span>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Deadline:</span>
                                                <span class="text-sm font-medium {{ $isOpen ? 'text-gray-900' : 'text-red-600' }}">
                                                    {{ $scholarship->application_deadline->format('M d, Y') }}
                                                </span>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-500">Applicants:</span>
                                                <span class="text-sm font-medium text-gray-900">
                                                    {{ $scholarship->current_applicants ?? 0 }}/{{ $scholarship->max_applicants ?? '∞' }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex gap-3">
                                            <a href="{{ route('partner.scholarships.show', $scholarship->id) }}"
                                               class="flex-1 text-center px-4 py-2 border border-green-600 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-300">
                                                View Details
                                            </a>
                                            @if($isOpen && in_array($scholarship->category, ['primary', 'secondary']))
                                                <a href="{{ route('partner.scholarships.create', ['scholarship_id' => $scholarship->id]) }}"
                                                   class="flex-1 text-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    Apply Now
                                                </a>
                                            @elseif($scholarship->category == 'university')
                                                <span class="flex-1 text-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed text-xs">
                                                    University Only
                                                </span>
                                            @else
                                                <span class="flex-1 text-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed">
                                                    {{ $isOpen ? 'Closed' : 'Deadline Passed' }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="flex justify-center">
                            {{ $scholarships->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Scholarships Found</h3>
                            <p class="text-gray-600 mb-6">No scholarships match your current search criteria. Try adjusting your filters or search terms.</p>
                            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                <a href="{{ route('partner.scholarships.index') }}" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-refresh mr-2"></i>
                                    Clear Filters
                                </a>
                                <button onclick="document.getElementById('search').value=''; document.getElementById('category').value=''; document.getElementById('status').value=''; document.querySelector('form').submit();"
                                        class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-times mr-2"></i>
                                    Reset Search
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    mobileMenuBtn.addEventListener('click', openMobileMenu);
    mobileSidebarOverlay.addEventListener('click', closeMobileMenu);
});
</script>
@endsection
