@extends('layouts.admin')

@section('title', 'User Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">User Management</h1>
        <div>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New User
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Users</div>
                            <div class="h4">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Users</div>
                            <div class="h4">{{ $stats['active'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">University Students</div>
                            <div class="h4">{{ $stats['university_students'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Partner Organizations</div>
                            <div class="h4">{{ $stats['partner_organizations'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Admins</div>
                            <div class="h4">{{ $stats['admins'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-shield fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-dark text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New This Month</div>
                            <div class="h4">{{ $stats['new_this_month'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-purple text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New Students</div>
                            <div class="h4">{{ $stats['new_students'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-teal text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">New Partners</div>
                            <div class="h4">{{ $stats['new_partners'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-handshake fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>All Users
            </h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search users..." id="searchUsers">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterRole">
                        <option value="">All Roles</option>
                        <option value="1">Normal User</option>
                        <option value="2">University Student</option>
                        <option value="3">Partner Organization</option>
                        <option value="4">Admin</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>User</th>
                            <th>Role & Details</th>
                            <th>Status</th>
                            <th>Documents</th>
                            <th>Activity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_users[]" value="{{ $user->id }}" class="form-check-input">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($user->profile_picture)
                                        <img src="{{ asset('storage/' . $user->profile_picture) }}" alt="{{ $user->first_name }}" class="rounded-circle me-3" width="50" height="50" style="object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            {{ strtoupper(substr($user->first_name ?? 'U', 0, 1)) }}
                                        </div>
                                    @endif
                                    <div>
                                        <div class="fw-bold">
                                            {{ $user->first_name }} {{ $user->last_name }}
                                        </div>
                                        <small class="text-muted">{{ $user->email }}</small><br>
                                        @if($user->phone_number)
                                            <small class="text-muted">{{ $user->phone_number }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $user->role_id === 4 ? 'danger' :
                                    ($user->role_id === 3 ? 'info' :
                                    ($user->role_id === 2 ? 'success' : 'secondary'))
                                }}">
                                    {{ $user->role_name }}
                                </span>
                                @if($user->role_id === 2 && $user->university_name)
                                    <br><small class="text-muted">{{ $user->university_name }}</small>
                                    @if($user->course_of_study)
                                        <br><small class="text-muted">{{ $user->course_of_study }} (Level {{ $user->current_level ?? 'N/A' }})</small>
                                    @endif
                                @elseif($user->role_id === 3 && $user->organization_name)
                                    <br><small class="text-muted">{{ $user->organization_name }}</small>
                                    @if($user->organization_type)
                                        <br><small class="text-muted">{{ ucfirst(str_replace('_', ' ', $user->organization_type)) }}</small>
                                    @endif
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $user->status === 'active' ? 'success' :
                                    ($user->status === 'suspended' ? 'danger' : 'warning')
                                }}">
                                    {{ ucfirst($user->status ?? 'inactive') }}
                                </span>
                                @if($user->email_verified_at)
                                    <br><small class="text-success"><i class="fas fa-check-circle"></i> Verified</small>
                                @else
                                    <br><small class="text-warning"><i class="fas fa-exclamation-circle"></i> Unverified</small>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    @if($user->profile_picture)
                                        <small class="text-success"><i class="fas fa-image"></i> Profile Picture</small>
                                    @endif
                                    @if($user->admission_letter)
                                        <small class="text-info"><i class="fas fa-file-pdf"></i> Admission Letter</small>
                                    @endif
                                    @if($user->scholarshipApplications->count() > 0)
                                        <small class="text-primary"><i class="fas fa-file-alt"></i> {{ $user->scholarshipApplications->count() }} Application(s)</small>
                                    @endif
                                    @if($user->role_id === 3 && $user->partnerStudents->count() > 0)
                                        <small class="text-warning"><i class="fas fa-users"></i> {{ $user->partnerStudents->count() }} Students</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>{{ $user->created_at ? $user->created_at->format('M d, Y') : 'N/A' }}</strong>
                                    @if($user->created_at)
                                        <br><small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                    @endif
                                    @if($user->last_login_at)
                                        <br><small class="text-success">Last: {{ $user->last_login_at->format('M d') }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($user->status === 'active')
                                        <form method="POST" action="{{ route('admin.users.update', $user->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="suspended">
                                            <input type="hidden" name="first_name" value="{{ $user->first_name }}">
                                            <input type="hidden" name="last_name" value="{{ $user->last_name }}">
                                            <input type="hidden" name="email" value="{{ $user->email }}">
                                            <input type="hidden" name="phone_number" value="{{ $user->phone_number }}">
                                            <input type="hidden" name="role" value="{{ $user->role }}">
                                            <button type="submit" class="btn btn-sm btn-outline-warning" title="Suspend">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.users.update', $user->id) }}" style="display: inline;">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="active">
                                            <input type="hidden" name="first_name" value="{{ $user->first_name }}">
                                            <input type="hidden" name="last_name" value="{{ $user->last_name }}">
                                            <input type="hidden" name="email" value="{{ $user->email }}">
                                            <input type="hidden" name="phone_number" value="{{ $user->phone_number }}">
                                            <input type="hidden" name="role" value="{{ $user->role }}">
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="Activate">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="Delete" onclick="deleteUser({{ $user->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No users found</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $users->firstItem() }} to {{ $users->lastItem() }} of {{ $users->total() }} results
                </div>
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteUserForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

// Search functionality
document.getElementById('searchUsers').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Filter functionality
document.getElementById('filterRole').addEventListener('change', function() {
    filterUsers();
});

document.getElementById('filterStatus').addEventListener('change', function() {
    filterUsers();
});

function filterUsers() {
    const roleFilter = document.getElementById('filterRole').value;
    const statusFilter = document.getElementById('filterStatus').value;
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const roleCell = row.querySelector('td:nth-child(3)');
        const statusCell = row.querySelector('td:nth-child(4)');
        
        const roleText = roleCell ? roleCell.textContent.toLowerCase() : '';
        const statusText = statusCell ? statusCell.textContent.toLowerCase() : '';
        
        const roleMatch = !roleFilter || roleText.includes(roleFilter);
        const statusMatch = !statusFilter || statusText.includes(statusFilter);
        
        row.style.display = (roleMatch && statusMatch) ? '' : 'none';
    });
}

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});
</script>
@endsection