<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class PartnerOrganization extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'school_account_number',
        'school_account_name',
        'bank_name',
        'bank_code',
        'payment_details_updated_at',
        'website',
        'description',
        'contact_person',
        'contact_person_title',
        'contact_person_email',
        'contact_person_phone',
        'established_year',
        'student_capacity',
        'current_students',
        'partnership_start_date',
        'partnership_status',
        'logo',
        'documents',
        'notes',
        'is_verified',
        'verification_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'established_year' => 'integer',
        'partnership_start_date' => 'date',
        'verification_date' => 'datetime',
        'documents' => 'array',
        'student_capacity' => 'integer',
        'current_students' => 'integer',
        'is_verified' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = ['type_display', 'partnership_status_display'];

    /**
     * Get the display format for organization type.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'school' => 'School',
            'university' => 'University',
            'ngo' => 'NGO',
            'government' => 'Government Agency',
            default => ucfirst(str_replace('_', ' ', $this->type))
        };
    }

    /**
     * Get the display format for partnership status.
     */
    public function getPartnershipStatusDisplayAttribute(): string
    {
        return match($this->partnership_status) {
            'active' => 'Active Partnership',
            'inactive' => 'Inactive Partnership',
            'terminated' => 'Partnership Terminated',
            'pending' => 'Partnership Pending',
            default => ucfirst($this->partnership_status)
        };
    }

    /**
     * Check if organization is verified.
     */
    public function isVerified(): bool
    {
        return $this->is_verified === true;
    }

    /**
     * Check if organization has active partnership.
     */
    public function hasActivePartnership(): bool
    {
        return $this->partnership_status === 'active';
    }

    /**
     * Check if organization is a school.
     */
    public function isSchool(): bool
    {
        return $this->type === 'school';
    }

    /**
     * Check if organization is a university.
     */
    public function isUniversity(): bool
    {
        return $this->type === 'university';
    }

    /**
     * Check if payment details are complete.
     */
    public function hasCompletePaymentDetails(): bool
    {
        return !empty($this->school_account_number) && 
               !empty($this->school_account_name) && 
               !empty($this->bank_name);
    }

    /**
     * Get payment details status.
     */
    public function getPaymentDetailsStatus(): string
    {
        if ($this->hasCompletePaymentDetails()) {
            return 'complete';
        }
        
        $missing = [];
        if (empty($this->school_account_number)) $missing[] = 'Account Number';
        if (empty($this->school_account_name)) $missing[] = 'Account Name';
        if (empty($this->bank_name)) $missing[] = 'Bank Name';
        
        return 'incomplete: ' . implode(', ', $missing);
    }

    /**
     * Get users associated with this organization.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'partner_organization_id');
    }

    /**
     * Get all students enrolled in this organization.
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class, 'school_id');
    }

    /**
     * Get all scholarship applications from this organization.
     */
    public function scholarshipApplications(): HasMany
    {
        return $this->hasMany(ScholarshipApplication::class, 'school_id');
    }



    /**
     * Scope for verified organizations.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('partnership_status', 'active');
    }

    /**
     * Scope for organizations with active partnerships.
     */
    public function scopeActivePartnership($query)
    {
        return $query->where('partnership_status', 'active');
    }

    /**
     * Scope for schools.
     */
    public function scopeSchools($query)
    {
        return $query->where('type', 'school');
    }

    /**
     * Scope for universities.
     */
    public function scopeUniversities($query)
    {
        return $query->where('type', 'university');
    }

    /**
     * Scope by organization type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by location.
     */
    public function scopeByLocation($query, $state = null, $city = null)
    {
        if ($state) {
            $query->where('state', $state);
        }
        if ($city) {
            $query->where('city', $city);
        }
        return $query;
    }
}
