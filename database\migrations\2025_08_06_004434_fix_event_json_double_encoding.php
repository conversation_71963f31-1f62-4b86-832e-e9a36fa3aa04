<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix double-encoded JSON fields in events table
        $events = DB::table('events')->get();
        
        foreach ($events as $event) {
            $updates = [];
            
            // Fix requirements field - handle double encoding
            if ($event->requirements) {
                $requirements = $this->fixDoubleEncodedJson($event->requirements);
                if ($requirements !== null) {
                    $updates['requirements'] = json_encode($requirements);
                }
            }
            
            // Fix agenda field - handle double encoding
            if ($event->agenda) {
                $agenda = $this->fixDoubleEncodedJson($event->agenda);
                if ($agenda !== null) {
                    $updates['agenda'] = json_encode($agenda);
                }
            }
            
            // Update the event if we have changes
            if (!empty($updates)) {
                DB::table('events')->where('id', $event->id)->update($updates);
            }
        }
    }

    /**
     * Fix double-encoded JSON data
     */
    private function fixDoubleEncodedJson($data)
    {
        // If it's already an array, return it
        if (is_array($data)) {
            return $data;
        }
        
        // If it's a string, try to decode it
        if (is_string($data)) {
            $decoded = json_decode($data, true);
            
            // If first decode works, check if the result is also JSON
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                // Check if the first element is also a JSON string
                if (count($decoded) > 0 && is_string($decoded[0])) {
                    $secondDecode = json_decode($decoded[0], true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($secondDecode)) {
                        return $secondDecode;
                    }
                }
                return $decoded;
            }
            
            // If it's not valid JSON, treat it as a single item
            return [$data];
        }
        
        return null;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this migration as it's just fixing data
    }
};
