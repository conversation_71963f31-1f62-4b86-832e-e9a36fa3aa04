@extends('layouts.admin')

@section('title', 'User Details')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">User Details</h1>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit User
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-3">
            <!-- Profile Picture Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">Profile Picture</h6>
                </div>
                <div class="card-body text-center">
                    @if($user->profile_picture)
                        <img src="{{ asset('storage/' . $user->profile_picture) }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    @else
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px; font-size: 3rem;">
                            {{ strtoupper(substr($user->first_name ?? 'U', 0, 1)) }}
                        </div>
                    @endif
                    <h4>{{ $user->first_name ?? 'User' }} {{ $user->last_name ?? 'Name' }}</h4>
                    <p class="text-muted">{{ $user->role_name ?? 'Normal User' }}</p>
                    <div class="row text-center">
                        <div class="col">
                            <span class="badge bg-{{ $user->status === 'active' ? 'success' : 'danger' }} p-2">
                                <i class="fas fa-circle me-1" style="font-size: 8px;"></i>
                                {{ ucfirst($user->status ?? 'active') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <div class="col-lg-9">
            <!-- Personal Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">Personal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">First Name</label>
                                <p class="form-control-plaintext">{{ $user->first_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Last Name</label>
                                <p class="form-control-plaintext">{{ $user->last_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Email</label>
                                <p class="form-control-plaintext">
                                    {{ $user->email ?? 'N/A' }}
                                    @if($user->email_verified_at)
                                        <span class="badge bg-success ms-2">Verified</span>
                                    @else
                                        <span class="badge bg-warning ms-2">Unverified</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Phone Number</label>
                                <p class="form-control-plaintext">{{ $user->phone_number ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Date of Birth</label>
                                <p class="form-control-plaintext">{{ $user->date_of_birth ? $user->date_of_birth->format('M d, Y') : 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Gender</label>
                                <p class="form-control-plaintext">{{ ucfirst($user->gender ?? 'N/A') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Address</label>
                                <p class="form-control-plaintext">
                                    {{ $user->address ?? 'N/A' }}<br>
                                    {{ $user->city ?? '' }} {{ $user->state ?? '' }} {{ $user->country ?? '' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role-Specific Information -->
            @if($user->role_id == 2) <!-- University Student -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-success text-white">
                    <h6 class="m-0 font-weight-bold">University Student - Comprehensive Information</h6>
                </div>
                <div class="card-body">
                    <!-- Basic Academic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Student ID</label>
                                <p class="form-control-plaintext">{{ $user->student_id ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Matriculation Number</label>
                                <p class="form-control-plaintext">{{ $user->matriculation_number ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">University</label>
                                <p class="form-control-plaintext">{{ $user->university_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Course of Study</label>
                                <p class="form-control-plaintext">{{ $user->course_of_study ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Year of Study</label>
                                <p class="form-control-plaintext">{{ $user->year_of_study ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Current Level</label>
                                <p class="form-control-plaintext">{{ $user->current_level ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">CGPA</label>
                                <p class="form-control-plaintext">{{ $user->cgpa ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Academic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Faculty</label>
                                <p class="form-control-plaintext">{{ $user->faculty ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Department</label>
                                <p class="form-control-plaintext">{{ $user->department ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Admission Year</label>
                                <p class="form-control-plaintext">{{ $user->admission_year ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Expected Graduation</label>
                                <p class="form-control-plaintext">{{ $user->expected_graduation_year ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Academic Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-{{ $user->academic_status === 'good_standing' ? 'success' : ($user->academic_status === 'probation' ? 'warning' : 'secondary') }}">
                                        {{ ucfirst(str_replace('_', ' ', $user->academic_status ?? 'N/A')) }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Tuition Fee Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-{{ $user->tuition_fee_status === 'paid' ? 'success' : ($user->tuition_fee_status === 'partial' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($user->tuition_fee_status ?? 'N/A') }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Outstanding Balance</label>
                                <p class="form-control-plaintext">
                                    @if($user->outstanding_balance)
                                        ₦{{ number_format($user->outstanding_balance) }}
                                    @else
                                        N/A
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Family Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Parent/Guardian Name</label>
                                <p class="form-control-plaintext">{{ $user->parent_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Parent/Guardian Phone</label>
                                <p class="form-control-plaintext">{{ $user->parent_phone ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Family Income</label>
                                <p class="form-control-plaintext">
                                    @if($user->family_income)
                                        ₦{{ number_format($user->family_income) }}
                                    @else
                                        N/A
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Number of Siblings</label>
                                <p class="form-control-plaintext">{{ $user->number_of_siblings ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    @if($user->special_needs || $user->medical_conditions || $user->disabilities || $user->extracurricular_activities)
                    <div class="additional-info mt-4 p-3 bg-light rounded">
                        <h6 class="text-warning mb-3">
                            <i class="fas fa-clipboard-list me-2"></i>
                            Additional Information
                        </h6>
                        <div class="row">
                            @if($user->special_needs)
                            <div class="col-md-6">
                                <strong>Special Needs:</strong><br>
                                <small class="text-muted">{{ $user->special_needs }}</small>
                            </div>
                            @endif
                            @if($user->medical_conditions)
                            <div class="col-md-6">
                                <strong>Medical Conditions:</strong><br>
                                <small class="text-muted">{{ $user->medical_conditions }}</small>
                            </div>
                            @endif
                            @if($user->disabilities)
                            <div class="col-md-6">
                                <strong>Disabilities:</strong><br>
                                <small class="text-muted">{{ $user->disabilities }}</small>
                            </div>
                            @endif
                            @if($user->extracurricular_activities)
                            <div class="col-md-6">
                                <strong>Extracurricular Activities:</strong><br>
                                <small class="text-muted">{{ $user->extracurricular_activities }}</small>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            @if($user->role_id == 3) <!-- Partner Organization -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">Organization Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Name</label>
                                <p class="form-control-plaintext">{{ $user->organization_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Type</label>
                                <p class="form-control-plaintext">{{ ucfirst(str_replace('_', ' ', $user->organization_type ?? 'N/A')) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Registration Number</label>
                                <p class="form-control-plaintext">{{ $user->organization_registration_number ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Phone</label>
                                <p class="form-control-plaintext">{{ $user->organization_phone ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Organization Address</label>
                                <p class="form-control-plaintext">{{ $user->organization_address ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Principal Name</label>
                                <p class="form-control-plaintext">{{ $user->principal_name ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Principal Email</label>
                                <p class="form-control-plaintext">{{ $user->principal_email ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Details Section -->
                    @if($partnerOrganization)
                    <div class="payment-details mt-4 p-3 bg-light rounded">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-credit-card me-2"></i>
                            Payment Details
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Account Number</label>
                                    <p class="form-control-plaintext">{{ $partnerOrganization->school_account_number ?? 'N/A' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Account Name</label>
                                    <p class="form-control-plaintext">{{ $partnerOrganization->school_account_name ?? 'N/A' }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Bank Name</label>
                                    <p class="form-control-plaintext">{{ $partnerOrganization->bank_name ?? 'N/A' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Bank Code</label>
                                    <p class="form-control-plaintext">{{ $partnerOrganization->bank_code ?? 'N/A' }}</p>
                                </div>
                            </div>
                        </div>
                        @if($partnerOrganization->payment_details_updated_at)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Payment Details Updated</label>
                                    <p class="form-control-plaintext">{{ \Carbon\Carbon::parse($partnerOrganization->payment_details_updated_at)->format('M d, Y g:i A') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label font-weight-bold">Payment Status</label>
                                    <p class="form-control-plaintext">
                                        <span class="badge bg-{{ $partnerOrganization->hasCompletePaymentDetails() ? 'success' : 'warning' }}">
                                            {{ $partnerOrganization->hasCompletePaymentDetails() ? 'Complete' : 'Incomplete' }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        <!-- Partner Contributions/Donations -->
                        @if($partnerDonations && $partnerDonations->count() > 0)
                        <div class="partner-contributions mt-3">
                            <h6 class="text-info mb-2">
                                <i class="fas fa-hand-holding-usd me-2"></i>
                                Partner Contributions ({{ $partnerDonations->count() }})
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Purpose</th>
                                            <th>Status</th>
                                            <th>Receipt</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($partnerDonations as $donation)
                                        <tr>
                                            <td>{{ $donation->created_at->format('M d, Y') }}</td>
                                            <td>₦{{ number_format($donation->amount) }}</td>
                                            <td>{{ $donation->purpose ?? 'General Contribution' }}</td>
                                            <td>
                                                <span class="badge bg-{{ $donation->payment_status === 'completed' ? 'success' : ($donation->payment_status === 'pending' ? 'warning' : 'danger') }}">
                                                    {{ ucfirst($donation->payment_status) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($donation->receipt_number)
                                                    <span class="badge bg-info">{{ $donation->receipt_number }}</span>
                                                @else
                                                    <span class="text-muted">No receipt</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>

            @if($partnerOrganization)
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-warning text-white">
                    <h6 class="m-0 font-weight-bold">Partner Organization Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Partnership Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-{{ $partnerOrganization->partnership_status === 'active' ? 'success' : 'warning' }}">
                                        {{ $partnerOrganization->partnership_status_display }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Verification Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-{{ $partnerOrganization->is_verified ? 'success' : 'warning' }}">
                                        {{ $partnerOrganization->is_verified ? 'Verified' : 'Pending Verification' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Student Capacity</label>
                                <p class="form-control-plaintext">{{ $partnerOrganization->student_capacity ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Current Students</label>
                                <p class="form-control-plaintext">{{ $partnerOrganization->current_students ?? 'N/A' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            @endif

            <!-- Documents Card -->
            @if(count($userDocuments) > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-secondary text-white">
                    <h6 class="m-0 font-weight-bold">Documents & Files</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($userDocuments as $document)
                        <div class="col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            @if(str_contains($document['name'], 'image') || str_contains($document['name'], 'photo'))
                                                <i class="fas fa-image fa-2x text-primary"></i>
                                            @elseif(str_contains($document['name'], 'pdf'))
                                                <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                            @else
                                                <i class="fas fa-file fa-2x text-secondary"></i>
                                            @endif
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $document['type'] }}</h6>
                                            <p class="mb-1 small text-muted">{{ $document['name'] }}</p>
                                            <p class="mb-0 small text-muted">{{ number_format($document['size'] / 1024, 2) }} KB</p>
                                        </div>
                                        <div>
                                            <a href="{{ $document['url'] }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif



            <!-- Partner Students (if partner organization) -->
            @if($user->role_id == 3 && $partnerStudents->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">Managed Students</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Student Name</th>
                                    <th>Class/Level</th>
                                    <th>Gender</th>
                                    <th>Age</th>
                                    <th>Status</th>
                                    <th>Scholarship Applications</th>
                                                                            <th>Student ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($partnerStudents as $student)
                                <tr>
                                    <td>
                                        <strong>{{ $student->name }}</strong>
                                        @if($student->parent_name)
                                        <br><small class="text-muted">Parent: {{ $student->parent_name }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $student->class ?? 'N/A' }}</span>
                                        @if($student->academic_performance)
                                        <br><small class="text-muted">{{ $student->academic_performance }}</small>
                                        @endif
                                    </td>
                                    <td>{{ ucfirst($student->gender ?? 'N/A') }}</td>
                                    <td>{{ $student->age ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ $student->status === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($student->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($student->scholarshipApplications && $student->scholarshipApplications->count() > 0)
                                            <span class="badge bg-info">{{ $student->scholarshipApplications->count() }} applications</span>
                                            <br>
                                            @foreach($student->scholarshipApplications->take(3) as $application)
                                                <div class="mb-1">
                                                    <strong class="text-primary">App #{{ $application->id }}</strong>
                                                    @if($application->scholarship)
                                                        <br><strong class="text-info">Scholarship #{{ $application->scholarship->id }}</strong>
                                                    @endif
                                                    <br><small class="text-muted">{{ $application->scholarship->title ?? 'N/A' }}</small>
                                                </div>
                                            @endforeach
                                            @if($student->scholarshipApplications->count() > 3)
                                                <small class="text-muted">+{{ $student->scholarshipApplications->count() - 3 }} more</small>
                                            @endif
                                        @else
                                            <span class="text-muted">No applications</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-muted">Student ID: #{{ $student->id }}</span>
                                        <br>
                                        <a href="{{ route('admin.users.students.download-pdf', $student->id) }}" class="btn btn-sm btn-outline-success mt-1" title="Download Student PDF">
                                            <i class="fas fa-download me-1"></i>Download PDF
                                        </a>
                                    </td>
                                </tr>

                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif

            <!-- Activity Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-dark text-white">
                    <h6 class="m-0 font-weight-bold">Activity Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Account Created</h6>
                                <p class="text-muted mb-0">{{ $user->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        @if($user->email_verified_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Email Verified</h6>
                                <p class="text-muted mb-0">{{ $user->email_verified_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        @if($user->last_login_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Last Login</h6>
                                <p class="text-muted mb-0">{{ $user->last_login_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        @endif
                        @if($user->scholarshipApplications->count() > 0)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">First Scholarship Application</h6>
                                <p class="text-muted mb-0">{{ $user->scholarshipApplications->first()->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-content {
    padding-left: 20px;
    border-left: 2px solid #e3e6f0;
    padding-bottom: 10px;
}

.timeline-item:last-child .timeline-content {
    border-left: none;
}
</style>

@endsection

@section('scripts')
<script>
// Test function to ensure JavaScript is working
console.log('Admin user view scripts loaded successfully');

function sendEmail(email) {
    window.open(`mailto:${email}`, '_blank');
}

function viewActivity(userId) {
    // Implement activity view functionality
    alert('Activity view functionality to be implemented');
}

function viewApplication(applicationId) {
    // For now, show application details in an alert
    // In a full implementation, this would open a modal or redirect to application details
    alert(`View application details for ID: ${applicationId}\n\nThis would open a detailed view of the scholarship application.`);
}

function approveApplication(applicationId) {
    if (confirm('Are you sure you want to approve this application?')) {
        // In a full implementation, this would make an AJAX call to approve the application
        alert(`Application ${applicationId} approved successfully!`);
        // Reload the page to show updated status
        location.reload();
    }
}

function rejectApplication(applicationId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason !== null) {
        // In a full implementation, this would make an AJAX call to reject the application
        alert(`Application ${applicationId} rejected. Reason: ${reason}`);
        // Reload the page to show updated status
        location.reload();
    }
}


</script>
@endsection 