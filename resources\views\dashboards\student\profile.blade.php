@extends('layouts.dashboard')

@section('title', 'Academic Profile - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/profile')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/profile')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Academic Profile</h1>
                            <p class="text-green-100 text-sm lg:text-base">Manage your university details and personal information</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white/20 rounded-lg px-4 py-2">
                                <span class="text-green-100 text-sm">Profile Completion:</span>
                                <span class="text-white font-bold ml-2">{{ $user->getProfileCompletionPercentage() }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="p-6 lg:p-8">
                <div class="max-w-4xl mx-auto">
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                <span class="text-green-800 font-medium">{{ session('success') }}</span>
                            </div>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-circle text-red-600 mr-2 mt-0.5"></i>
                                <div>
                                    <span class="text-red-800 font-medium block">Please correct the following errors:</span>
                                    <ul class="text-red-700 text-sm mt-1 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('student.profile.update') }}" enctype="multipart/form-data" class="space-y-8">
                        @csrf

                        <!-- Personal Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-user text-green-600 mr-2"></i>
                                    Personal Information
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Update your basic personal details</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                        <input type="text" id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                        <input type="text" id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                        <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input type="tel" id="phone_number" name="phone_number" value="{{ old('phone_number', $user->phone_number) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                                        <input type="date" id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth', $user->date_of_birth) }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                                        <select id="gender" name="gender" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                            <option value="">Select Gender</option>
                                            <option value="male" {{ old('gender', $user->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                            <option value="female" {{ old('gender', $user->gender) === 'female' ? 'selected' : '' }}>Female</option>
                                            <option value="other" {{ old('gender', $user->gender) === 'other' ? 'selected' : '' }}>Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-graduation-cap text-green-600 mr-2"></i>
                                    Academic Information
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Your university and academic details</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="university_name" class="block text-sm font-medium text-gray-700 mb-2">University Name *</label>
                                        <input type="text" id="university_name" name="university_name" value="{{ old('university_name', $user->university_name) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                                                <label for="matriculation_number" class="block text-sm font-medium text-gray-700 mb-2">Matriculation Number *</label>
                        <input type="text" id="matriculation_number" name="matriculation_number" value="{{ old('matriculation_number', $user->matriculation_number) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="course_of_study" class="block text-sm font-medium text-gray-700 mb-2">Course of Study *</label>
                                        <input type="text" id="course_of_study" name="course_of_study" value="{{ old('course_of_study', $user->course_of_study) }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    </div>
                                    <div>
                                        <label for="current_level" class="block text-sm font-medium text-gray-700 mb-2">Current Level *</label>
                                        <select id="current_level" name="current_level" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                            <option value="">Select Level</option>
                                            <option value="100" {{ old('current_level', $user->current_level) === '100' ? 'selected' : '' }}>100 Level</option>
                                            <option value="200" {{ old('current_level', $user->current_level) === '200' ? 'selected' : '' }}>200 Level</option>
                                            <option value="300" {{ old('current_level', $user->current_level) === '300' ? 'selected' : '' }}>300 Level</option>
                                            <option value="400" {{ old('current_level', $user->current_level) === '400' ? 'selected' : '' }}>400 Level</option>
                                            <option value="500" {{ old('current_level', $user->current_level) === '500' ? 'selected' : '' }}>500 Level</option>
                                            <option value="600" {{ old('current_level', $user->current_level) === '600' ? 'selected' : '' }}>600 Level</option>
                                            <option value="postgraduate" {{ old('current_level', $user->current_level) === 'postgraduate' ? 'selected' : '' }}>Postgraduate</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="admission_letter" class="block text-sm font-medium text-gray-700 mb-2">
                                            Admission Letter 
                                            @if($user->current_level === '100' && empty($user->admission_letter))
                                                <span class="text-red-500">*</span>
                                            @endif
                                        </label>
                                        <input type="file" id="admission_letter" name="admission_letter" 
                                               accept=".pdf,.jpg,.jpeg,.png"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        @if($user->admission_letter)
                                            <div class="mt-2">
                                                <a href="{{ $user->admission_letter_url }}" target="_blank" class="text-green-600 hover:text-green-500 text-sm">
                                                    <i class="fas fa-file-pdf mr-1"></i>View Current Admission Letter
                                                </a>
                                            </div>
                                        @endif
                                        <p class="text-gray-500 text-xs mt-1">Accepted formats: PDF, JPG, JPEG, PNG (Max: 2MB)</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-map-marker-alt text-green-600 mr-2"></i>
                                    Address Information
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Your contact address details</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 gap-6">
                                    <div>
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                                        <textarea id="address" name="address" rows="3" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">{{ old('address', $user->address) }}</textarea>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                            <input type="text" id="city" name="city" value="{{ old('city', $user->city) }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        </div>
                                        <div>
                                            <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State</label>
                                            <input type="text" id="state" name="state" value="{{ old('state', $user->state) }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-save mr-2"></i>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});
</script>
@endsection
