<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Volunteer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Volunteers",
 *     description="Admin API Endpoints for Volunteer Management"
 * )
 */
class AdminVolunteerController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/volunteers",
     *     summary="Get all volunteers for admin",
     *     tags={"Admin Volunteers"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Volunteers retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            
            $query = Volunteer::with(['user'])
                ->orderBy('created_at', 'desc');

            if ($status) {
                $query->where('application_status', $status);
            }

            $volunteers = $query->paginate($perPage);

            // Calculate summary statistics
            $stats = [
                'total_volunteers' => Volunteer::count(),
                'approved_volunteers' => Volunteer::where('application_status', 'approved')->count(),
                'pending_volunteers' => Volunteer::where('application_status', 'pending')->count(),
                'rejected_volunteers' => Volunteer::where('application_status', 'rejected')->count(),
                'active_volunteers' => Volunteer::where('application_status', 'approved')->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Volunteers retrieved successfully',
                'data' => [
                    'volunteers' => $volunteers,
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve volunteers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/volunteers/{id}",
     *     summary="Get specific volunteer details",
     *     tags={"Admin Volunteers"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Volunteer retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $volunteer = Volunteer::with(['user'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Volunteer retrieved successfully',
                'data' => $volunteer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Volunteer not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/volunteers/{id}",
     *     summary="Update volunteer information",
     *     tags={"Admin Volunteers"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Volunteer updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $volunteer = Volunteer::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'application_status' => 'in:pending,approved,rejected',

                'notes' => 'nullable|string',
                'background_check_status' => 'in:pending,passed,failed,not_required',
                'background_check_date' => 'nullable|date'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $volunteer->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Volunteer updated successfully',
                'data' => $volunteer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update volunteer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/volunteers/{id}",
     *     summary="Delete a volunteer",
     *     tags={"Admin Volunteers"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Volunteer deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $volunteer = Volunteer::findOrFail($id);
            $volunteer->delete();

            return response()->json([
                'success' => true,
                'message' => 'Volunteer deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete volunteer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve volunteer application
     */
    public function approve($id): JsonResponse
    {
        try {
            $volunteer = Volunteer::findOrFail($id);
            
            $volunteer->update([
                'application_status' => 'approved',
                'approved_at' => now()
            ]);

            // Update user role if needed
            if ($volunteer->user) {
                $volunteer->user->assignRole('volunteer');
            }

            return response()->json([
                'success' => true,
                'message' => 'Volunteer application approved successfully',
                'data' => $volunteer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve volunteer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject volunteer application
     */
    public function reject(Request $request, $id): JsonResponse
    {
        try {
            $volunteer = Volunteer::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'rejection_reason' => 'required|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $volunteer->update([
                'application_status' => 'rejected',
                'rejection_reason' => $request->rejection_reason,
                'rejected_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Volunteer application rejected',
                'data' => $volunteer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject volunteer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update background check status
     */
    public function backgroundCheck(Request $request, $id): JsonResponse
    {
        try {
            $volunteer = Volunteer::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'background_check_status' => 'required|in:passed,failed,not_required',
                'background_check_notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $volunteer->update([
                'background_check_status' => $request->background_check_status,
                'background_check_date' => now(),
                'background_check_notes' => $request->background_check_notes
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Background check status updated successfully',
                'data' => $volunteer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update background check',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get volunteer statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_applications' => Volunteer::count(),
                'pending_applications' => Volunteer::where('application_status', 'pending')->count(),
                'approved_volunteers' => Volunteer::where('application_status', 'approved')->count(),
                'rejected_applications' => Volunteer::where('application_status', 'rejected')->count(),
                'active_volunteers' => Volunteer::where('application_status', 'approved')->count(),
                'volunteers_this_month' => Volunteer::whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year)
                    ->count(),
                'background_checks_pending' => Volunteer::where('background_check_status', 'pending')->count(),
                'background_checks_passed' => Volunteer::where('background_check_status', 'passed')->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Volunteer statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
