@extends('layouts.dashboard')

@section('title', 'Apply for Scholarship - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/scholarships')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/scholarships')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Apply for Scholarship</h1>
                            <p class="text-green-100 text-sm lg:text-base">{{ $scholarship->title }}</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="{{ route('student.scholarships') }}" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Scholarships
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Form -->
            <div class="p-6 lg:p-8">
                <div class="max-w-4xl mx-auto">
                    @if(session('error'))
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                                <span class="text-red-800 font-medium">{{ session('error') }}</span>
                            </div>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-circle text-red-600 mr-2 mt-0.5"></i>
                                <div>
                                    <span class="text-red-800 font-medium block">Please correct the following errors:</span>
                                    <ul class="text-red-700 text-sm mt-1 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Scholarship Info -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Scholarship Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600">Scholarship Title</p>
                                <p class="font-medium text-gray-900">{{ $scholarship->title }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Amount</p>
                                <p class="font-medium text-gray-900">₦{{ number_format($scholarship->amount) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Deadline</p>
                                <p class="font-medium text-gray-900">{{ \Carbon\Carbon::parse($scholarship->application_deadline)->format('M d, Y') }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Category</p>
                                <p class="font-medium text-gray-900">{{ ucfirst($scholarship->category) }}</p>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('student.scholarships.submit', $scholarship->id) }}" enctype="multipart/form-data" class="space-y-8">
                        @csrf

                        <!-- Personal Information (Read-only) -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-user text-green-600 mr-2"></i>
                                    Personal Information
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Your profile information (read-only)</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                        <input type="text" value="{{ $user->first_name }} {{ $user->last_name }}" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                        <input type="email" value="{{ $user->email }}" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">University</label>
                                        <input type="text" value="{{ $user->university_name }}" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Course of Study</label>
                                        <input type="text" value="{{ $user->course_of_study }}" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Level</label>
                                        <input type="text" value="{{ $user->current_level }} Level" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Matriculation Number</label>
                                        <input type="text" value="{{ $user->matriculation_number }}" readonly
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Application Details -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-edit text-green-600 mr-2"></i>
                                    Application Details
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Please provide the following information</p>
                            </div>
                            <div class="p-6">
                                <div class="space-y-6">
                                    <div>
                                        <label for="personal_statement" class="block text-sm font-medium text-gray-700 mb-2">
                                            Personal Statement <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="personal_statement" name="personal_statement" rows="6" required
                                                  placeholder="Tell us about yourself, your background, and why you deserve this scholarship..."
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">{{ old('personal_statement') }}</textarea>
                                        <p class="text-gray-500 text-xs mt-1">Minimum 100 characters, maximum 1000 characters</p>
                                    </div>

                                    <div>
                                        <label for="academic_achievements" class="block text-sm font-medium text-gray-700 mb-2">
                                            Academic Achievements <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="academic_achievements" name="academic_achievements" rows="4" required
                                                  placeholder="List your academic achievements, awards, and honors..."
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">{{ old('academic_achievements') }}</textarea>
                                        <p class="text-gray-500 text-xs mt-1">Minimum 50 characters, maximum 500 characters</p>
                                    </div>

                                    <div>
                                        <label for="financial_need" class="block text-sm font-medium text-gray-700 mb-2">
                                            Financial Need <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="financial_need" name="financial_need" rows="4" required
                                                  placeholder="Explain your financial situation and why you need this scholarship..."
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">{{ old('financial_need') }}</textarea>
                                        <p class="text-gray-500 text-xs mt-1">Minimum 50 characters, maximum 500 characters</p>
                                    </div>

                                    <div>
                                        <label for="future_goals" class="block text-sm font-medium text-gray-700 mb-2">
                                            Future Goals <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="future_goals" name="future_goals" rows="4" required
                                                  placeholder="Describe your career goals and how this scholarship will help you achieve them..."
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">{{ old('future_goals') }}</textarea>
                                        <p class="text-gray-500 text-xs mt-1">Minimum 50 characters, maximum 500 characters</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Required Documents -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="p-6 border-b border-gray-100">
                                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-file-upload text-green-600 mr-2"></i>
                                    Required Documents
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Upload the following documents</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="academic_transcript" class="block text-sm font-medium text-gray-700 mb-2">
                                            Academic Transcript <span class="text-red-500">*</span>
                                        </label>
                                        <input type="file" id="academic_transcript" name="academic_transcript" required
                                               accept=".pdf,.jpg,.jpeg,.png"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        <p class="text-gray-500 text-xs mt-1">PDF, JPG, JPEG, PNG (Max: 2MB)</p>
                                    </div>

                                    <div>
                                        <label for="passport_photo" class="block text-sm font-medium text-gray-700 mb-2">
                                            Passport Photograph <span class="text-red-500">*</span>
                                        </label>
                                        <input type="file" id="passport_photo" name="passport_photo" required
                                               accept=".jpg,.jpeg,.png"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        <p class="text-gray-500 text-xs mt-1">JPG, JPEG, PNG (Max: 1MB)</p>
                                    </div>

                                    <div>
                                        <label for="id_card" class="block text-sm font-medium text-gray-700 mb-2">
                                            ID Card/Certificate <span class="text-red-500">*</span>
                                        </label>
                                        <input type="file" id="id_card" name="id_card" required
                                               accept=".pdf,.jpg,.jpeg,.png"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        <p class="text-gray-500 text-xs mt-1">PDF, JPG, JPEG, PNG (Max: 2MB)</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('student.scholarships') }}" class="inline-flex items-center px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});
</script>
@endsection 