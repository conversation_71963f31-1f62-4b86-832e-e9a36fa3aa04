<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Donation;
use App\Models\DonationCampaign;
use App\Models\User;

class DonationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_can_create_anonymous_donation_with_bank_transfer()
    {
        $donationData = [
            'donor_name' => '<PERSON>',
            'donor_email' => '<EMAIL>',
            'donor_phone' => '+*************',
            'amount' => 15000,
            'currency' => 'NGN',
            'donation_type' => 'one-time',
            'payment_method' => 'bank_transfer',
            'purpose' => 'Education Support',
            'is_anonymous' => false,
            'notes' => 'Test donation'
        ];

        $response = $this->postJson('/api/v1/donations', $donationData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Donation created successfully. Please complete the bank transfer.',
                ])
                ->assertJsonStructure([
                    'data' => [
                        'donation',
                        'payment_method',
                        'bank_details' => [
                            'account_name',
                            'account_number',
                            'bank_name'
                        ],
                        'transaction_reference',
                        'instructions'
                    ]
                ]);

        $this->assertDatabaseHas('donations', [
            'donor_name' => '<PERSON> Doe',
            'donor_email' => '<EMAIL>',
            'donor_phone' => '+*************',
            'amount' => 15000,
            'currency' => 'NGN',
            'donation_type' => 'one-time',
            'payment_method' => 'bank_transfer',
            'payment_status' => 'pending',
            'is_anonymous' => false
        ]);
    }

    public function test_can_create_anonymous_donation_with_paystack()
    {
        $donationData = [
            'donor_name' => 'Jane Smith',
            'donor_email' => '<EMAIL>',
            'amount' => 15000,
            'donation_type' => 'one-time',
            'payment_method' => 'paystack',
            'is_anonymous' => true
        ];

        $response = $this->postJson('/api/v1/donations', $donationData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Donation created successfully. Redirecting to payment...',
                ])
                ->assertJsonStructure([
                    'data' => [
                        'donation',
                        'payment_url',
                        'transaction_reference'
                    ]
                ]);

        $this->assertDatabaseHas('donations', [
            'donor_name' => 'Jane Smith',
            'donor_email' => '<EMAIL>',
            'amount' => 15000,
            'donation_type' => 'one-time',
            'payment_method' => 'paystack',
            'payment_status' => 'pending',
            'is_anonymous' => true
        ]);
    }

    public function test_can_get_donation_campaigns()
    {
        // Create a test campaign
        $campaign = DonationCampaign::factory()->create([
            'status' => 'active',
            'start_date' => now()->subDays(1),
            'end_date' => now()->addDays(30)
        ]);

        $response = $this->getJson('/api/v1/donations/campaigns');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title',
                                'description',
                                'goal_amount',
                                'raised_amount',
                                'status'
                            ]
                        ]
                    ]
                ]);
    }

    public function test_can_verify_payment_status()
    {
        $donation = Donation::factory()->create([
            'transaction_reference' => 'TXN_TEST123456',
            'payment_status' => 'pending'
        ]);

        $response = $this->getJson('/api/v1/donations/verify/TXN_TEST123456');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Payment status retrieved'
                ])
                ->assertJsonStructure([
                    'data' => [
                        'donation',
                        'status',
                        'is_completed'
                    ]
                ]);
    }

    public function test_donation_validation_fails_with_invalid_data()
    {
        $invalidData = [
            'donor_name' => '', // Required field empty
            'donor_email' => 'invalid-email', // Invalid email
            'amount' => 50, // Below minimum amount
            'donation_type' => 'invalid-type', // Invalid donation type
            'payment_method' => 'invalid-method' // Invalid payment method
        ];

        $response = $this->postJson('/api/v1/donations', $invalidData);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation error'
                ])
                ->assertJsonStructure([
                    'errors'
                ]);
    }
}
