---
alwaysApply: false
---
# Halimakq Foundation- <PERSON>vel + Next.js NGO Platform
# Cursor AI Assistant Rules

## PROJECT OVERVIEW
This is a comprehensive NGO platform built with Laravel backend API and Next.js frontend for the Halimatu Abdullahi Kofa Knowledge Quest Foundation. The platform manages donations, scholarships, volunteers, events, and educational programs.

## TECHNOLOGY STACK
- **Backend**: Laravel 12 with PHP 8.2+
- **Frontend**: Next.js 14+ with TypeScript
- **Authentication**: Laravel Sanctum (API tokens)
- **Database**: MySQL with migrations and seeders
- **Styling**: Tailwind CSS with shadcn/ui components
- **Icons**: Lucide React
- **Image Processing**: Intervention Image (Laravel)
- **File Storage**: Laravel Storage (public disk)
- **API Documentation**: L5 Swagger

## PROJECT STRUCTURE

### Backend (Laravel)
```
app/
├── Http/Controllers/Api/          # API controllers
│   ├── AuthController.php         # Authentication endpoints
│   ├── UserController.php         # User profile management
│   ├── BlogController.php         # Blog management
│   ├── DonationController.php     # Donation processing
│   ├── EventController.php        # Event management
│   ├── Admin/                     # Admin-only controllers
│   └── ...
├── Models/                        # Eloquent models
├── Providers/                     # Service providers
└── ...
routes/api.php                     # API routes definition
database/
├── migrations/                    # Database migrations
├── seeders/                       # Database seeders
└── factories/                     # Model factories
```

### Frontend (Next.js)
```
front-end/
├── app/                           # App router pages
│   ├── auth/                      # Authentication pages
│   ├── dashboard/                 # User dashboard
│   ├── page.tsx                   # Homepage
│   └── ...
├── components/                    # Reusable components
│   ├── ui/                        # shadcn/ui components
│   ├── navigation.tsx             # Main navigation
│   ├── footer.tsx                 # Site footer
│   └── ...
├── lib/                           # Utilities and configurations
│   ├── api.ts                     # API client
│   ├── utils.ts                   # Helper functions
│   └── ...
├── hooks/                         # Custom React hooks
└── styles/                        # Global styles
```

## CODING STANDARDS

### Laravel Backend Rules
1. **API Controllers**: Always extend from base Controller, use proper HTTP status codes
2. **Authentication**: Use Sanctum middleware for protected routes
3. **Validation**: Use Form Request classes for complex validation
4. **Resources**: Use API Resources for data transformation
5. **Models**: Define relationships, use soft deletes where appropriate
6. **Migrations**: Always use descriptive names, include foreign key constraints
7. **Error Handling**: Return JSON responses with consistent structure:
   ```php
   return response()->json([
       'success' => true/false,
       'message' => 'Description',
       'data' => $data,
       'errors' => $errors // only on validation failures
   ], $statusCode);
   ```

### Next.js Frontend Rules
1. **Components**: Use TypeScript with proper prop types
2. **Client Components**: Always add "use client" directive for components using hooks
3. **API Integration**: Use the centralized `apiClient` from `@/lib/api.ts`
4. **Styling**: Use Tailwind classes, prefer shadcn/ui components
5. **State Management**: Use React hooks (useState, useEffect, useContext)
6. **Error Handling**: Always handle loading states and errors gracefully
7. **Hydration**: Use `suppressHydrationWarning` for components affected by browser extensions

### File Naming Conventions
- **Laravel**: PascalCase for classes, snake_case for files/tables
- **Next.js**: kebab-case for files, PascalCase for components
- **Components**: Use descriptive names ending in component type (e.g., `UserProfileCard.tsx`)
- **Pages**: Use semantic names matching routes

## NGO-SPECIFIC BUSINESS LOGIC

### User Types and Roles
```typescript
type UserType = "student" | "partner" | "volunteer" | "admin"
```
- **Students**: Can apply for scholarships, register for events
- **Partners**: Organizations collaborating with the NGO
- **Volunteers**: Can log hours, participate in programs
- **Admin**: Full platform management access

### Key Entities
1. **Users**: Core user management with profiles and QR codes
2. **Donations**: Campaign-based donation system with payment tracking
3. **Scholarships**: Application and review system
4. **Events**: Registration and attendance tracking
5. **Volunteer Programs**: Hour logging and opportunity management
6. **Blog**: Content management with categories and comments
7. **Settings**: Dynamic platform configuration

### Database Patterns
- Use UUIDs for public-facing IDs (short_id format: HLTKKQ0001)
- Soft deletes for most entities
- Audit trails for sensitive operations
- Foreign key constraints with proper cascading

## API PATTERNS

### Endpoint Structure
```
/api/v1/
├── auth/                 # Authentication endpoints
├── profile/              # User profile management
├── donations/            # Donation processing
├── scholarships/         # Scholarship applications
├── events/               # Event management
├── blog/                 # Blog content
└── admin/                # Admin-only endpoints
```

### Request/Response Format
```typescript
// Request
interface ApiRequest {
  [key: string]: any;
}

// Response
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
}
```

### Authentication Flow
1. Login with email/password → receive access token
2. Store token in localStorage
3. Include token in Authorization header for API requests
4. Handle token expiration gracefully

## UI/UX PATTERNS

### Component Structure
```typescript
interface ComponentProps {
  // Define all props with proper types
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Component logic
  return (
    // JSX with Tailwind classes
  );
}
```

### Color Scheme
- **Primary**: Green variants (green-500, green-600, green-700)
- **Secondary**: Amber variants (amber-400, amber-500, amber-600)
- **Success**: Green
- **Error**: Red
- **Warning**: Amber
- **Info**: Blue

### Responsive Design
- Mobile-first approach
- Use Tailwind responsive prefixes (sm:, md:, lg:, xl:)
- Test on mobile, tablet, and desktop viewports

## SECURITY CONSIDERATIONS

### Frontend Security
- Validate all user inputs
- Sanitize data before API calls
- Handle authentication state properly
- Use HTTPS in production
- Implement proper error boundaries

### Backend Security
- Use Laravel's built-in security features
- Validate and sanitize all inputs
- Use proper authorization checks
- Implement rate limiting
- Use CORS properly for API access

## DEVELOPMENT WORKFLOW

### When Adding New Features
1. **Backend**: Create migration → model → controller → routes → test
2. **Frontend**: Create types → API integration → components → pages
3. **Testing**: Test API endpoints and frontend functionality
4. **Documentation**: Update API docs and component docs

### Common Tasks
1. **Adding new API endpoint**: Update routes, create controller method, test with frontend
2. **Creating new page**: Add to app directory, implement with proper TypeScript
3. **Adding form**: Use React Hook Form with validation, integrate with API
4. **File uploads**: Use Laravel storage, handle on frontend with proper validation

## MEMORY AND PERSISTENCE
- Use localStorage for authentication tokens
- Use React state for temporary UI state
- Use database for all persistent data
- Cache API responses where appropriate
- Use React Query/SWR for data fetching (if implemented)

## ERROR HANDLING PATTERNS

### Frontend Error Handling
```typescript
try {
  const data = await apiClient.someMethod();
  // Handle success
} catch (error) {
  console.error('Operation failed:', error);
  // Show user-friendly error message
}
```

### Backend Error Handling
```php
try {
    // Operation logic
    return response()->json(['success' => true, 'data' => $data]);
} catch (Exception $e) {
    Log::error('Operation failed', ['error' => $e->getMessage()]);
    return response()->json(['success' => false, 'message' => 'Operation failed'], 500);
}
```

## PERFORMANCE GUIDELINES
- Lazy load components where possible
- Optimize images and use Next.js Image component
- Use proper database indexing
- Implement pagination for large datasets
- Cache frequently accessed data
- Minimize API calls with proper data fetching strategies

## ACCESSIBILITY
- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation works
- Maintain good color contrast
- Test with screen readers
- Use proper heading hierarchy

This project serves a noble cause of educational empowerment in Nigeria. Always consider the NGO's mission when making technical decisions and prioritize user experience for beneficiaries, donors, and volunteers. 