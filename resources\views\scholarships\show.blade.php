@extends('layouts.app')

@section('title', $scholarship->title)

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">{{ $scholarship->title }}</h1>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-8">
                            <h4>Description</h4>
                            <p class="text-muted">{{ $scholarship->description }}</p>

                            <h4>Eligibility Criteria</h4>
                            <p class="text-muted">{{ $scholarship->eligibility_criteria }}</p>

                            <h4>Requirements</h4>
                            <p class="text-muted">{{ $scholarship->requirements }}</p>

                            @if($scholarship->application_instructions)
                                <h4>Application Instructions</h4>
                                <p class="text-muted">{{ $scholarship->application_instructions }}</p>
                            @endif

                            @if($scholarship->terms_conditions)
                                <h4>Terms & Conditions</h4>
                                <p class="text-muted">{{ $scholarship->terms_conditions }}</p>
                            @endif
                        </div>

                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5>Scholarship Details</h5>
                                    
                                    <div class="mb-3">
                                        <strong>Amount:</strong><br>
                                        <span class="text-primary">₦{{ number_format($scholarship->amount) }}</span>
                                    </div>

                                    <div class="mb-3">
                                        <strong>Category:</strong><br>
                                        <span class="badge bg-info">{{ ucfirst($scholarship->category) }}</span>
                                    </div>

                                    <div class="mb-3">
                                        <strong>Application Deadline:</strong><br>
                                        <span class="text-danger">{{ $scholarship->application_deadline->format('F j, Y') }}</span>
                                    </div>

                                    <div class="mb-3">
                                        <strong>Status:</strong><br>
                                        @if($scholarship->is_open)
                                            <span class="badge bg-success">Open for Applications</span>
                                        @else
                                            <span class="badge bg-secondary">Closed</span>
                                        @endif
                                    </div>

                                    @if($scholarship->max_applicants)
                                        <div class="mb-3">
                                            <strong>Available Slots:</strong><br>
                                            <span class="text-info">{{ $scholarship->max_applicants - $scholarship->current_applicants }} remaining</span>
                                        </div>
                                    @endif

                                    @auth
                                        @if($scholarship->is_open && $scholarship->application_deadline > now())
                                            <a href="{{ route('scholarships.apply', $scholarship->id) }}" 
                                               class="btn btn-primary btn-lg w-100">
                                                Apply Now
                                            </a>
                                        @else
                                            <button class="btn btn-secondary btn-lg w-100" disabled>
                                                Applications Closed
                                            </button>
                                        @endif
                                    @else
                                        <a href="{{ route('login') }}" class="btn btn-primary btn-lg w-100">
                                            Login to Apply
                                        </a>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 