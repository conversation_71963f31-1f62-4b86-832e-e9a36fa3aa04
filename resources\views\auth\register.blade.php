@extends('auth.layout')

@section('title', 'Register')
@section('header-title', 'Join <PERSON><PERSON>IM<PERSON>KQ FOUNDATION')
@section('header-subtitle', 'Choose your account type to get started')

@section('content')
<div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <!-- Clean Header -->
    <div class="text-center mb-8">
        <h1 class="text-2xl md:text-3xl font-semibold text-gray-900 mb-2">Create Account</h1>
        <p class="text-gray-600">Join HALIMAKQ Foundation</p>
    </div>

         <!-- Clean Form Container - Full Width -->
     <div class="max-w-6xl mx-auto">
         <div class="bg-white rounded-xl shadow-sm border border-gray-200">
             <div class="p-6 md:p-8 lg:p-10">

            <!-- Form Content -->
            <div class="p-6 md:p-8">
                @if ($errors->any())
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <span class="text-red-800 font-medium">Please fix the following errors:</span>
                        </div>
                        <ul class="text-red-700 text-sm mt-2 space-y-1">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if (session('success'))
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-green-800">{{ session('success') }}</span>
                        </div>
                    </div>
                @endif

                <form method="POST" action="{{ route('register') }}" id="registrationForm" class="space-y-6">
        @csrf

        <!-- Clean Role Selection -->
        <div class="mb-8">
            <label class="block text-sm font-medium text-gray-700 mb-4">Account Type <span class="text-red-500">*</span></label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <label class="relative cursor-pointer">
                    <input type="radio" name="role_id" value="1" class="sr-only" {{ old('role_id') == '1' ? 'checked' : '' }} required>
                    <div class="border-2 border-gray-200 rounded-lg p-6 text-center hover:border-green-300 transition-colors">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div class="text-base font-medium text-gray-900">Normal User</div>
                        <div class="text-sm text-gray-500 mt-1">Browse content and resources</div>
                    </div>
                </label>

                <label class="relative cursor-pointer">
                    <input type="radio" name="role_id" value="2" class="sr-only" {{ old('role_id') == '2' ? 'checked' : '' }} required>
                    <div class="border-2 border-gray-200 rounded-lg p-6 text-center hover:border-green-300 transition-colors">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-graduation-cap text-purple-600"></i>
                        </div>
                        <div class="text-base font-medium text-gray-900">University Student</div>
                        <div class="text-sm text-gray-500 mt-1">Apply for university scholarships</div>
                    </div>
                </label>

                <label class="relative cursor-pointer">
                    <input type="radio" name="role_id" value="3" class="sr-only" {{ old('role_id') == '3' ? 'checked' : '' }} required>
                    <div class="border-2 border-gray-200 rounded-lg p-6 text-center hover:border-green-300 transition-colors">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-school text-green-600"></i>
                        </div>
                        <div class="text-base font-medium text-gray-900">Partner Organization</div>
                        <div class="text-sm text-gray-500 mt-1">Manage students and applications</div>
                    </div>
                </label>
            </div>
            @error('role_id')
                <p class="mt-3 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Basic Information -->
        <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                        First Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors @error('first_name') border-red-300 @enderror"
                           id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                    @error('first_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Last Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors @error('last_name') border-red-300 @enderror"
                           id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                    @error('last_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email"
                           class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors @error('email') border-red-300 @enderror"
                           id="email" name="email" value="{{ old('email') }}" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    <input type="tel"
                           class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors @error('phone_number') border-red-300 @enderror"
                           id="phone_number" name="phone_number" value="{{ old('phone_number') }}" required>
                    @error('phone_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    Password <span class="text-red-500">*</span>
                </label>
                <input type="password"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('password') border-red-300 @enderror"
                       id="password" name="password" required>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                    Confirm Password <span class="text-red-500">*</span>
                </label>
                <input type="password"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200"
                       id="password_confirmation" name="password_confirmation" required>
            </div>
        </div>

        <!-- University Student Fields -->
        <div id="student-fields" class="bg-green-50 border border-green-200 rounded-lg p-6 space-y-4" style="display: none;">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-graduation-cap text-white text-sm"></i>
                </div>
                <h5 class="text-green-800 font-semibold">Academic Information</h5>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Student ID</label>
                    <input type="text"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('student_id') border-red-300 @enderror"
                           id="student_id" name="student_id" value="{{ old('student_id') }}">
                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label for="matriculation_number" class="block text-sm font-medium text-gray-700 mb-2">Matriculation Number</label>
                    <input type="text"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('matriculation_number') border-red-300 @enderror"
                           id="matriculation_number" name="matriculation_number" value="{{ old('matriculation_number') }}">
                    @error('matriculation_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div>
                <label for="university_name" class="block text-sm font-medium text-gray-700 mb-2">
                    University Name <span class="text-red-500">*</span>
                </label>
                <input type="text"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('university_name') border-red-300 @enderror"
                       id="university_name" name="university_name" value="{{ old('university_name') }}">
                @error('university_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="course_of_study" class="block text-sm font-medium text-gray-700 mb-2">
                        Course of Study <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('course_of_study') border-red-300 @enderror"
                           id="course_of_study" name="course_of_study" value="{{ old('course_of_study') }}">
                    @error('course_of_study')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label for="year_of_study" class="block text-sm font-medium text-gray-700 mb-2">Year of Study</label>
                    <select class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('year_of_study') border-red-300 @enderror"
                            id="year_of_study" name="year_of_study">
                        <option value="">Select Year</option>
                        <option value="1" {{ old('year_of_study') == '1' ? 'selected' : '' }}>1st Year</option>
                        <option value="2" {{ old('year_of_study') == '2' ? 'selected' : '' }}>2nd Year</option>
                        <option value="3" {{ old('year_of_study') == '3' ? 'selected' : '' }}>3rd Year</option>
                        <option value="4" {{ old('year_of_study') == '4' ? 'selected' : '' }}>4th Year</option>
                        <option value="5" {{ old('year_of_study') == '5' ? 'selected' : '' }}>5th Year</option>
                        <option value="6" {{ old('year_of_study') == '6' ? 'selected' : '' }}>6th Year</option>
                    </select>
                    @error('year_of_study')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div>
                <label for="cgpa" class="block text-sm font-medium text-gray-700 mb-2">CGPA (Optional)</label>
                <input type="number" step="0.01" min="0" max="5"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('cgpa') border-red-300 @enderror"
                       id="cgpa" name="cgpa" value="{{ old('cgpa') }}">
                @error('cgpa')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Partner Organization Fields -->
        <div id="organization-fields" class="bg-gray-50 rounded-lg p-6 space-y-6" style="display: none;">
            <div class="flex items-center mb-6">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-school text-green-600"></i>
                </div>
                <h5 class="text-xl font-medium text-gray-900">Organization Information</h5>
            </div>

            <div>
                <label for="organization_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Organization Name <span class="text-red-500">*</span>
                </label>
                <input type="text"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_name') border-red-300 @enderror"
                       id="organization_name" name="organization_name" value="{{ old('organization_name') }}">
                @error('organization_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="organization_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Organization Type <span class="text-red-500">*</span>
                    </label>
                    <select class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_type') border-red-300 @enderror"
                            id="organization_type" name="organization_type">
                        <option value="">Select Type</option>
                        <option value="primary_school" {{ old('organization_type') == 'primary_school' ? 'selected' : '' }}>Primary School</option>
                        <option value="secondary_school" {{ old('organization_type') == 'secondary_school' ? 'selected' : '' }}>Secondary School</option>
                        <option value="college" {{ old('organization_type') == 'college' ? 'selected' : '' }}>College</option>
                        <option value="ngo" {{ old('organization_type') == 'ngo' ? 'selected' : '' }}>NGO</option>
                        <option value="other" {{ old('organization_type') == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('organization_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label for="organization_registration_number" class="block text-sm font-medium text-gray-700 mb-2">Registration Number</label>
                    <input type="text"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_registration_number') border-red-300 @enderror"
                           id="organization_registration_number" name="organization_registration_number"
                           value="{{ old('organization_registration_number') }}">
                    @error('organization_registration_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div>
                <label for="organization_address" class="block text-sm font-medium text-gray-700 mb-2">
                    Organization Address <span class="text-red-500">*</span>
                </label>
                <textarea class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_address') border-red-300 @enderror"
                          id="organization_address" name="organization_address" rows="3">{{ old('organization_address') }}</textarea>
                @error('organization_address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="organization_phone" class="block text-sm font-medium text-gray-700 mb-2">Organization Phone</label>
                    <input type="tel"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_phone') border-red-300 @enderror"
                           id="organization_phone" name="organization_phone" value="{{ old('organization_phone') }}">
                    @error('organization_phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label for="organization_email" class="block text-sm font-medium text-gray-700 mb-2">Organization Email</label>
                    <input type="email"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('organization_email') border-red-300 @enderror"
                           id="organization_email" name="organization_email" value="{{ old('organization_email') }}">
                    @error('organization_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="border-t border-gray-200 pt-4">
                <h6 class="text-gray-600 font-medium mb-4">Principal/Head Information</h6>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="principal_name" class="block text-sm font-medium text-gray-700 mb-2">Principal/Head Name</label>
                        <input type="text"
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('principal_name') border-red-300 @enderror"
                               id="principal_name" name="principal_name" value="{{ old('principal_name') }}">
                        @error('principal_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="principal_phone" class="block text-sm font-medium text-gray-700 mb-2">Principal Phone</label>
                        <input type="tel"
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('principal_phone') border-red-300 @enderror"
                               id="principal_phone" name="principal_phone" value="{{ old('principal_phone') }}">
                        @error('principal_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <label for="principal_email" class="block text-sm font-medium text-gray-700 mb-2">Principal Email</label>
                    <input type="email"
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('principal_email') border-red-300 @enderror"
                           id="principal_email" name="principal_email" value="{{ old('principal_email') }}">
                    @error('principal_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Payment Details Section -->
            <div class="border-t border-gray-200 pt-4 mt-4">
                <div class="flex items-center mb-4">
                    <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-2">
                        <i class="fas fa-credit-card text-green-600 text-sm"></i>
                    </div>
                    <h6 class="text-base font-medium text-gray-900">Payment Details</h6>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="school_account_number" class="block text-sm font-medium text-gray-700 mb-2">
                            School Account Number <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('school_account_number') border-red-300 @enderror"
                               id="school_account_number" name="school_account_number" value="{{ old('school_account_number') }}"
                               placeholder="e.g., **********">
                        @error('school_account_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="school_account_name" class="block text-sm font-medium text-gray-700 mb-2">
                            School Account Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('school_account_name') border-red-300 @enderror"
                               id="school_account_name" name="school_account_name" value="{{ old('school_account_name') }}"
                               placeholder="e.g., ABC Primary School">
                        @error('school_account_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label for="bank_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Bank Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('bank_name') border-red-300 @enderror"
                               id="bank_name" name="bank_name" value="{{ old('bank_name') }}"
                               placeholder="e.g., First Bank of Nigeria">
                        @error('bank_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                </div>
            </div>
        </div>

        <!-- Additional Common Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                <input type="date"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('date_of_birth') border-red-300 @enderror"
                       id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth') }}">
                @error('date_of_birth')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <div>
                <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                <select class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('gender') border-red-300 @enderror"
                        id="gender" name="gender">
                    <option value="">Select Gender</option>
                    <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Male</option>
                    <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Female</option>
                    <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('gender')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div>
            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
            <textarea class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('address') border-red-300 @enderror"
                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
            @error('address')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                <input type="text"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('city') border-red-300 @enderror"
                       id="city" name="city" value="{{ old('city') }}">
                @error('city')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <div>
                <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State</label>
                <input type="text"
                       class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('state') border-red-300 @enderror"
                       id="state" name="state" value="{{ old('state') }}">
                @error('state')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

                <!-- Submit Button -->
        <div class="pt-8">
            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-4 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <i class="fas fa-user-plus mr-2"></i>Create Account
            </button>
        </div>

        <!-- Footer -->
        <div class="text-center pt-8 border-t border-gray-200">
            <p class="text-gray-600 mb-3">Already have an account?</p>
            <a href="{{ route('login') }}" class="text-green-600 hover:text-green-500 transition-colors">Sign in here</a>
        </div>
            </div>
        </div>
    </form>
</div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Role selection functionality
    const roleInputs = document.querySelectorAll('input[name="role_id"]');
    const organizationFields = document.getElementById('organization-fields');
    
    function toggleOrganizationFields() {
        const selectedRole = document.querySelector('input[name="role_id"]:checked');
        if (selectedRole && selectedRole.value === '3') {
            organizationFields.style.display = 'block';
        } else {
            organizationFields.style.display = 'none';
        }
    }
    
    // Add event listeners to radio buttons
    roleInputs.forEach(input => {
        input.addEventListener('change', toggleOrganizationFields);
    });
    
    // Initialize on page load
    toggleOrganizationFields();
    
    // Add visual feedback for role selection
    roleInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Remove selected state from all cards
            document.querySelectorAll('input[name="role_id"]').forEach(radio => {
                const card = radio.closest('label').querySelector('div');
                card.classList.remove('border-green-500', 'bg-green-50');
                card.classList.add('border-gray-200');
            });
            
            // Add selected state to current card
            if (this.checked) {
                const card = this.closest('label').querySelector('div');
                card.classList.remove('border-gray-200');
                card.classList.add('border-green-500', 'bg-green-50');
            }
        });
    });
    
    // Initialize selected state on page load
    const checkedRole = document.querySelector('input[name="role_id"]:checked');
    if (checkedRole) {
        const card = checkedRole.closest('label').querySelector('div');
        card.classList.remove('border-gray-200');
        card.classList.add('border-green-500', 'bg-green-50');
    }
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role_id');
    const studentFields = document.getElementById('student-fields');
    const organizationFields = document.getElementById('organization-fields');

    // Function to toggle role-specific fields
    function toggleRoleFields() {
        const selectedRole = roleSelect.value;

        // Hide all role-specific fields first
        studentFields.style.display = 'none';
        organizationFields.style.display = 'none';

        // Show relevant fields based on selected role
        if (selectedRole === '2') {
            studentFields.style.display = 'block';
        } else if (selectedRole === '3') {
            organizationFields.style.display = 'block';
        }
    }

    // Handle role selection change
    roleSelect.addEventListener('change', toggleRoleFields);

    // Set initial state based on current selection (for form validation errors)
    toggleRoleFields();
});
</script>
@endpush
