<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PartnerProfileController extends Controller
{
    /**
     * Display the partner organization profile.
     */
    public function show()
    {
        $partner = Auth::user();
        
        // Ensure user is a partner organization
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        return view('partner.profile.show', compact('partner'));
    }

    /**
     * Show the form for editing the partner organization profile.
     */
    public function edit()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        return view('partner.profile.edit', compact('partner'));
    }

    /**
     * Update the partner organization profile.
     */
    public function update(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users')->ignore($partner->id),
            ],
            'phone' => 'nullable|string|max:20',
            'organization_name' => 'required|string|max:255',
            'organization_type' => 'required|string|in:school,ngo,foundation,government,private',
            'registration_number' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'school_account_number' => 'required|string|max:50',
            'school_account_name' => 'required|string|max:255',
            'bank_name' => 'required|string|max:255',
            'bank_code' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'established_year' => 'nullable|integer|min:1800|max:' . date('Y'),
            'student_capacity' => 'nullable|integer|min:1|max:10000',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'current_password' => 'nullable|string|min:8',
            'new_password' => 'nullable|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle password change
        if ($request->filled('current_password') && $request->filled('new_password')) {
            if (!Hash::check($request->current_password, $partner->password)) {
                return redirect()->back()
                    ->withErrors(['current_password' => 'The current password is incorrect.'])
                    ->withInput();
            }
        }

        // Handle profile photo upload
        $profilePhotoPath = $partner->profile_photo;
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($profilePhotoPath) {
                Storage::disk('public')->delete($profilePhotoPath);
            }
            
            $file = $request->file('profile_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $profilePhotoPath = $file->storeAs('partner_profiles', $filename, 'public');
        }

        // Prepare update data
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'organization_name' => $request->organization_name,
            'organization_type' => $request->organization_type,
            'registration_number' => $request->registration_number,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'school_account_number' => $request->school_account_number,
            'school_account_name' => $request->school_account_name,
            'bank_name' => $request->bank_name,
            'bank_code' => $request->bank_code,
            'payment_details_updated_at' => now(),
            'website' => $request->website,
            'description' => $request->description,
            'established_year' => $request->established_year,
            'student_capacity' => $request->student_capacity,
            'profile_photo' => $profilePhotoPath,
        ];

        // Add password if being changed
        if ($request->filled('new_password')) {
            $updateData['password'] = Hash::make($request->new_password);
        }

        try {
            // Update partner profile
            $partner->update($updateData);

            // Check if payment details were updated
            $paymentDetailsUpdated = $request->filled('school_account_number') && 
                                    $request->filled('school_account_name') && 
                                    $request->filled('bank_name');

            $successMessage = 'Profile updated successfully!';
            if ($paymentDetailsUpdated) {
                $successMessage .= ' Payment details have been updated and are now ready for scholarship payments.';
            }

            return redirect()->route('partner.profile.show')
                ->with('success', $successMessage);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Partner profile update failed: ' . $e->getMessage());
            
            return redirect()->back()
                ->withErrors(['general' => 'Profile update failed. Please try again. Error: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show the settings page for the partner organization.
     */
    public function settings()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        return view('partner.profile.settings', compact('partner'));
    }

    /**
     * Update partner organization settings.
     */
    public function updateSettings(Request $request)
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $validator = Validator::make($request->all(), [
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'application_notifications' => 'boolean',
            'newsletter_subscription' => 'boolean',
            'public_profile' => 'boolean',
            'show_contact_info' => 'boolean',
            'auto_approve_applications' => 'boolean',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare settings data
        $settings = [
            'email_notifications' => $request->boolean('email_notifications'),
            'sms_notifications' => $request->boolean('sms_notifications'),
            'application_notifications' => $request->boolean('application_notifications'),
            'newsletter_subscription' => $request->boolean('newsletter_subscription'),
            'public_profile' => $request->boolean('public_profile'),
            'show_contact_info' => $request->boolean('show_contact_info'),
            'auto_approve_applications' => $request->boolean('auto_approve_applications'),
            'timezone' => $request->timezone,
            'language' => $request->language,
        ];

        // Update settings
        $partner->update(['settings' => $settings]);

        return redirect()->route('partner.profile.settings')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Delete partner organization profile photo.
     */
    public function deletePhoto()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        if ($partner->profile_photo) {
            Storage::disk('public')->delete($partner->profile_photo);
            $partner->update(['profile_photo' => null]);
        }

        return redirect()->back()
            ->with('success', 'Profile photo deleted successfully!');
    }

    /**
     * Get partner organization statistics for profile dashboard.
     */
    public function getStatistics()
    {
        $partner = Auth::user();
        
        if (!$partner->isPartnerOrganization()) {
            abort(403, 'Access denied. Partner organization access required.');
        }

        $stats = [
            'total_students' => $partner->partnerStudents()->count(),
            'active_students' => $partner->partnerStudents()->where('status', 'active')->count(),
            'total_applications' => $partner->scholarshipApplications()->count(),
            'pending_applications' => $partner->scholarshipApplications()->where('status', 'pending')->count(),
            'approved_applications' => $partner->scholarshipApplications()->where('status', 'approved')->count(),
            'profile_completion' => $this->calculateProfileCompletion($partner),
        ];

        return response()->json($stats);
    }

    /**
     * Calculate profile completion percentage.
     */
    private function calculateProfileCompletion($partner)
    {
        $fields = [
            'name', 'email', 'phone', 'organization_name', 'organization_type',
            'address', 'city', 'state', 'country', 'description', 'website'
        ];

        $completed = 0;
        $total = count($fields);

        foreach ($fields as $field) {
            if (!empty($partner->$field)) {
                $completed++;
            }
        }

        // Add bonus for profile photo
        if ($partner->profile_photo) {
            $completed += 0.5;
            $total += 0.5;
        }

        return round(($completed / $total) * 100);
    }
}
