<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('partner_organizations', function (Blueprint $table) {
            // Payment Details
            $table->string('school_account_number')->nullable()->after('postal_code');
            $table->string('school_account_name')->nullable()->after('school_account_number');
            $table->string('bank_name')->nullable()->after('school_account_name');
            $table->string('bank_code')->nullable()->after('bank_name');
            $table->string('payment_details_updated_at')->nullable()->after('bank_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('partner_organizations', function (Blueprint $table) {
            $table->dropColumn([
                'school_account_number',
                'school_account_name',
                'bank_name',
                'bank_code',
                'payment_details_updated_at'
            ]);
        });
    }
}; 