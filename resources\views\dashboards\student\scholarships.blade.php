@extends('layouts.dashboard')

@section('title', 'Available Scholarships - Student Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="student"
                :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/scholarships')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="student"
                    :menuItems="App\Helpers\SidebarConfig::getStudentSidebar('/student/scholarships')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="mb-4 lg:mb-0">
                            <h1 class="text-2xl lg:text-3xl font-bold mb-2">Available Scholarships</h1>
                            <p class="text-green-100 text-sm lg:text-base">Browse and apply for university scholarships</p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="/student/applications" class="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors duration-300">
                                <i class="fas fa-file-alt mr-2"></i>
                                My Applications
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="p-6 lg:p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                        <form method="GET" action="{{ route('student.scholarships') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input type="text" id="search" name="search" value="{{ request('search') }}" 
                                       placeholder="Search scholarships..." 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            </div>
                            <div>
                                <label for="min_amount" class="block text-sm font-medium text-gray-700 mb-2">Min Amount (₦)</label>
                                <input type="number" id="min_amount" name="min_amount" value="{{ request('min_amount') }}" 
                                       placeholder="0" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            </div>
                            <div>
                                <label for="max_amount" class="block text-sm font-medium text-gray-700 mb-2">Max Amount (₦)</label>
                                <input type="number" id="max_amount" name="max_amount" value="{{ request('max_amount') }}" 
                                       placeholder="1000000" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            </div>
                            <div class="flex items-end">
                                <button type="submit" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-search mr-2"></i>
                                    Filter
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Scholarships Grid -->
                    @if($scholarships->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            @foreach($scholarships as $scholarship)
                                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-300">
                                    <div class="p-6">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                <i class="fas fa-graduation-cap text-white text-lg"></i>
                                            </div>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                University
                                            </span>
                                        </div>

                                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                            {{ $scholarship->title }}
                                        </h3>

                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                            {{ $scholarship->description }}
                                        </p>

                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center text-sm text-gray-600">
                                                <i class="fas fa-money-bill-wave text-green-500 mr-2"></i>
                                                <span class="font-medium text-green-600">₦{{ number_format($scholarship->amount) }}</span>
                                            </div>
                                            <div class="flex items-center text-sm text-gray-600">
                                                <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>
                                                <span>Deadline: {{ \Carbon\Carbon::parse($scholarship->application_deadline)->format('M d, Y') }}</span>
                                            </div>
                                            <div class="flex items-center text-sm text-gray-600">
                                                <i class="fas fa-users text-purple-500 mr-2"></i>
                                                <span>{{ $scholarship->max_applications ?? 'Unlimited' }} spots</span>
                                            </div>
                                        </div>

                                        <div class="flex flex-col gap-2">
                                            @if(in_array($scholarship->id, $appliedScholarshipIds))
                                                <div class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg">
                                                    <i class="fas fa-check-circle mr-2"></i>
                                                    Already Applied
                                                </div>
                                            @else
                                                <a href="{{ route('student.scholarships.apply', $scholarship->id) }}" 
                                                   class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                                    <i class="fas fa-paper-plane mr-2"></i>
                                                    Apply Now
                                                </a>
                                            @endif
                                            <button onclick="viewScholarship({{ $scholarship->id }})" 
                                                    class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-300">
                                                <i class="fas fa-eye mr-2"></i>
                                                View Details
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Deadline indicator -->
                                    @php
                                        $daysLeft = \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($scholarship->application_deadline), false);
                                    @endphp
                                    @if($daysLeft <= 7 && $daysLeft > 0)
                                        <div class="px-6 py-3 bg-orange-50 border-t border-orange-100">
                                            <div class="flex items-center text-orange-700">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                <span class="text-sm font-medium">{{ $daysLeft }} day{{ $daysLeft > 1 ? 's' : '' }} left to apply</span>
                                            </div>
                                        </div>
                                    @elseif($daysLeft <= 0)
                                        <div class="px-6 py-3 bg-red-50 border-t border-red-100">
                                            <div class="flex items-center text-red-700">
                                                <i class="fas fa-times-circle mr-2"></i>
                                                <span class="text-sm font-medium">Application deadline passed</span>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($scholarships->hasPages())
                            <div class="flex justify-center">
                                {{ $scholarships->appends(request()->query())->links() }}
                            </div>
                        @endif
                    @else
                        <!-- No Scholarships -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Scholarships Available</h3>
                            <p class="text-gray-600 mb-6">
                                @if(request()->hasAny(['search', 'min_amount', 'max_amount']))
                                    No scholarships match your current filters. Try adjusting your search criteria.
                                @else
                                    There are currently no university scholarships available for application.
                                @endif
                            </p>
                            @if(request()->hasAny(['search', 'min_amount', 'max_amount']))
                                <a href="{{ route('student.scholarships') }}" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                                    <i class="fas fa-refresh mr-2"></i>
                                    Clear Filters
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scholarship Details Modal -->
<div id="scholarshipModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Scholarship Details</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="scholarshipContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    mobileMenuBtn?.addEventListener('click', function() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    });

    mobileSidebarOverlay?.addEventListener('click', function() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    });
});

function viewScholarship(scholarshipId) {
    // Show loading state
    document.getElementById('scholarshipContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';
    document.getElementById('scholarshipModal').classList.remove('hidden');
    
    // In a real implementation, you would fetch scholarship details via AJAX
    // For now, we'll show a placeholder
    setTimeout(() => {
        document.getElementById('scholarshipContent').innerHTML = `
            <div class="space-y-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Scholarship Description</h4>
                    <p class="text-gray-600">Detailed scholarship information would be loaded here via AJAX call to the backend.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Eligibility Criteria</h4>
                    <p class="text-gray-600">Eligibility requirements would be displayed here.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">Required Documents</h4>
                    <p class="text-gray-600">List of required documents would be shown here.</p>
                </div>
                <div class="flex gap-3">
                    <button class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-300">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Apply Now
                    </button>
                    <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                        Close
                    </button>
                </div>
            </div>
        `;
    }, 500);
}

function closeModal() {
    document.getElementById('scholarshipModal').classList.add('hidden');
}
</script>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
