@extends('layouts.dashboard')

@section('title', 'Partner Dashboard')

@section('content')
@php
    $partnerOrg = Auth::user()->partnerOrganization;
    $hasPaymentDetails = $partnerOrg && $partnerOrg->hasCompletePaymentDetails();
    $paymentStatus = $partnerOrg ? $partnerOrg->getPaymentDetailsStatus() : 'No organization found';
@endphp
<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 min-h-screen lg:block hidden">
            <x-dashboard.sidebar
                userType="partner"
                :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner-dashboard')"
            />
        </div>

        <!-- Mobile Menu Button -->
        <div class="lg:hidden fixed top-4 left-4 z-50">
            <button id="mobile-menu-btn" class="bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg shadow-lg transition-colors duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar" class="lg:hidden fixed inset-0 z-40 hidden">
            <div class="absolute inset-0 bg-black/50" id="mobile-sidebar-overlay"></div>
            <div class="relative w-64 h-full bg-white shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-sidebar-content">
                <x-dashboard.sidebar
                    userType="partner"
                    :menuItems="App\Helpers\SidebarConfig::getPartnerSidebar('/partner-dashboard')"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-0">
            <!-- Simple Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
                        <p class="text-gray-600 mt-1">Manage your students and applications</p>
                    </div>
                    <div class="flex gap-3">
                        @if($partnerOrg && !$hasPaymentDetails)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-exclamation-triangle mr-1"></i>Payment Details Required
                            </span>
                        @endif
                        <a href="{{ route('partner.students.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                            Add Student
                        </a>
                        <a href="{{ route('partner.applications.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                            New Application
                        </a>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="p-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Simple Statistics -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-users text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Students</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ Auth::user()->partnerStudents()->count() }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-alt text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Applications</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) { $q->whereIn('status', ['pending', 'under_review']); })->count() }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Approved</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ Auth::user()->partnerStudents()->whereHas('scholarshipApplications', function($q) { $q->where('status', 'approved'); })->count() }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-graduation-cap text-purple-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Scholarships</p>
                                    <p class="text-xl font-semibold text-gray-900">{{ \App\Models\Scholarship::where('status', 'open')->whereIn('category', ['primary', 'secondary'])->count() }}</p>
                                </div>
                            </div>
                        </div>
                    </div>


                    
                    @if($partnerOrg && !$hasPaymentDetails)
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 shadow-lg">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-exclamation-triangle text-red-600 text-lg"></i>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-red-800">
                                            ⚠️ Payment Details Required
                                        </h3>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-clock mr-1"></i>Action Required
                                        </span>
                                    </div>
                                    <div class="mt-3 text-sm text-red-700">
                                        <p class="mb-2"><strong>Your school's payment details are incomplete.</strong> This is required to receive scholarship payments for your students.</p>
                                        <div class="bg-red-100 p-3 rounded-lg">
                                            <p class="font-medium mb-1">Missing Information:</p>
                                            <p class="text-red-800">{{ $paymentStatus }}</p>
                                        </div>
                                    </div>
                                    <div class="mt-4 flex gap-3">
                                        <a href="{{ route('partner.profile.edit') }}" class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-300 shadow-sm">
                                            <i class="fas fa-edit mr-2"></i>Update Payment Details Now
                                        </a>
                                        <button onclick="dismissWarning()" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 transition-colors duration-300">
                                            <i class="fas fa-times mr-2"></i>Dismiss
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif($partnerOrg && $hasPaymentDetails)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">
                                        ✅ Payment Details Complete
                                    </h3>
                                    <p class="text-sm text-green-700">Your payment details are up to date and ready for scholarship payments.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <a href="{{ route('partner.students.index') }}" class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-users text-green-600"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">Manage Students</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Add and manage your students for scholarship applications</p>
                        </a>

                        <a href="{{ route('partner.scholarships.index') }}" class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-graduation-cap text-purple-600"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">Browse Scholarships</h3>
                            </div>
                            <p class="text-gray-600 text-sm">View available scholarships and apply for your students</p>
                        </a>

                        <a href="{{ route('partner.applications.index') }}" class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-alt text-blue-600"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">View Applications</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Track application status and monitor progress</p>
                        </a>
                    </div>

                    <!-- Recent Students -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-medium text-gray-900">Recent Students</h2>
                            <a href="{{ route('partner.students.index') }}" class="text-green-600 hover:text-green-700 text-sm font-medium">
                                View All
                            </a>
                        </div>
                        
                        @php
                            $recentStudents = Auth::user()->partnerStudents()->latest()->take(5)->get();
                        @endphp
                        
                        @if($recentStudents->count() > 0)
                            <div class="space-y-3">
                                @foreach($recentStudents as $student)
                                    <div class="flex items-center justify-between p-3 border border-gray-100 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-user text-green-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900 text-sm">{{ $student->name }}</h4>
                                                <p class="text-xs text-gray-600">Class {{ $student->class }} • {{ $student->gender }}</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($student->status === 'active') bg-green-100 text-green-700
                                            @else bg-gray-100 text-gray-700
                                            @endif">
                                            {{ ucfirst($student->status) }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-user-plus text-gray-400"></i>
                                </div>
                                <h3 class="text-sm font-medium text-gray-900 mb-1">No students yet</h3>
                                <p class="text-xs text-gray-600 mb-3">Start by adding your first student</p>
                                <a href="{{ route('partner.students.create') }}" class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-xs font-medium rounded-lg hover:bg-green-700 transition-colors duration-300">
                                    <i class="fas fa-plus mr-1"></i>Add Student
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Function to dismiss payment warning
function dismissWarning() {
    const warningElement = document.querySelector('.bg-red-50');
    if (warningElement) {
        warningElement.style.display = 'none';
        // Store dismissal in localStorage for 24 hours
        localStorage.setItem('paymentWarningDismissed', Date.now());
    }
}

// Check if warning was dismissed recently
document.addEventListener('DOMContentLoaded', function() {
    const dismissed = localStorage.getItem('paymentWarningDismissed');
    if (dismissed) {
        const dismissedTime = parseInt(dismissed);
        const now = Date.now();
        const hoursSinceDismissed = (now - dismissedTime) / (1000 * 60 * 60);
        
        // If dismissed more than 24 hours ago, show warning again
        if (hoursSinceDismissed < 24) {
            const warningElement = document.querySelector('.bg-red-50');
            if (warningElement) {
                warningElement.style.display = 'none';
            }
        } else {
            localStorage.removeItem('paymentWarningDismissed');
        }
    }
});
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebarContent = document.getElementById('mobile-sidebar-content');

    function openMobileMenu() {
        mobileSidebar.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebarContent.classList.remove('-translate-x-full');
        }, 10);
    }

    function closeMobileMenu() {
        mobileSidebarContent.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileSidebar.classList.add('hidden');
        }, 300);
    }

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', openMobileMenu);
    }

    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', closeMobileMenu);
    }
});
</script>
@endsection
