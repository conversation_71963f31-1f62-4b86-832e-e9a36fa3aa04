<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix existing events where requirements and agenda might be stored as strings
        $events = DB::table('events')->get();
        
        foreach ($events as $event) {
            $updates = [];
            
            // Fix requirements field
            if ($event->requirements && is_string($event->requirements)) {
                $requirements = json_decode($event->requirements, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($requirements)) {
                    $updates['requirements'] = json_encode($requirements);
                } else {
                    // If it's not valid JSON, treat it as a single requirement
                    $updates['requirements'] = json_encode([$event->requirements]);
                }
            }
            
            // Fix agenda field
            if ($event->agenda && is_string($event->agenda)) {
                $agenda = json_decode($event->agenda, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($agenda)) {
                    $updates['agenda'] = json_encode($agenda);
                } else {
                    // If it's not valid JSON, treat it as a single agenda item
                    $updates['agenda'] = json_encode([$event->agenda]);
                }
            }
            
            // Update the event if we have changes
            if (!empty($updates)) {
                DB::table('events')->where('id', $event->id)->update($updates);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this migration as it's just fixing data
    }
};
