<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create partner organizations for existing partner users
        $partnerUsers = DB::table('users')->where('role', 'partner')->get();
        
        foreach ($partnerUsers as $user) {
            // Create a partner organization for each partner user
            $orgId = DB::table('partner_organizations')->insertGetId([
                'name' => $user->organization_name ?? 'Partner Organization ' . $user->id,
                'type' => $user->organization_type ?? 'school',
                'email' => $user->organization_email ?? $user->email,
                'phone' => $user->organization_phone ?? $user->phone_number,
                'address' => $user->organization_address ?? null,
                'city' => $user->city ?? null,
                'state' => $user->state ?? null,
                'country' => $user->country ?? 'Nigeria',
                'contact_person' => $user->principal_name ?? $user->first_name . ' ' . $user->last_name,
                'contact_person_email' => $user->principal_email ?? $user->email,
                'contact_person_phone' => $user->principal_phone ?? $user->phone_number,
                'partnership_status' => 'active',
                'is_verified' => true,
                'verification_date' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            // Update the user to link to the organization
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'partner_organization_id' => $orgId,
                    'updated_at' => now(),
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove partner_organization_id from users
        DB::table('users')
            ->where('role', 'partner')
            ->update(['partner_organization_id' => null]);
        
        // Delete partner organizations
        DB::table('partner_organizations')->truncate();
    }
};
