<?php

namespace Database\Factories;

use App\Models\Scholarship;
use App\Models\User;
use App\Models\ScholarshipApplication;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ScholarshipApplication>
 */
class ScholarshipApplicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ScholarshipApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'scholarship_id' => Scholarship::factory(),
            'student_id' => User::factory()->create(['role' => 'student'])->id,
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected', 'under_review']),
            'application_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'academic_year' => $this->faker->randomElement(['2024-2025', '2025-2026', '2026-2027']),
            'gpa' => $this->faker->randomFloat(2, 2.0, 4.0),
            'family_income' => $this->faker->numberBetween(50000, 500000),
            'family_size' => $this->faker->numberBetween(2, 8),
            'personal_statement' => $this->faker->paragraphs(3, true),
            'financial_need_statement' => $this->faker->paragraphs(2, true),
            'academic_achievements' => $this->faker->paragraphs(2, true),
            'extracurricular_activities' => $this->faker->paragraphs(2, true),
            'community_service' => $this->faker->paragraphs(2, true),
            'leadership_experience' => $this->faker->paragraphs(2, true),
            'career_goals' => $this->faker->paragraphs(2, true),
            'documents_submitted' => json_encode([
                'transcript' => 'transcript.pdf',
                'recommendation' => 'recommendation.pdf',
                'financial_documents' => 'financial.pdf'
            ]),
            'reviewer_notes' => $this->faker->optional()->paragraph(),
            'review_date' => $this->faker->optional()->dateTimeBetween('-6 months', 'now'),
            'reviewer_id' => $this->faker->optional()->numberBetween(1, 10),
            'award_amount' => $this->faker->optional()->numberBetween(50000, 200000),
            'award_date' => $this->faker->optional()->dateTimeBetween('-3 months', 'now'),
            'disbursement_date' => $this->faker->optional()->dateTimeBetween('-2 months', 'now'),
            'disbursement_method' => $this->faker->optional()->randomElement(['bank_transfer', 'check', 'cash']),
            'bank_details' => $this->faker->optional()->json_encode([
                'bank_name' => $this->faker->company(),
                'account_number' => $this->faker->numerify('##########'),
                'account_name' => $this->faker->name()
            ]),
            'terms_accepted' => true,
            'terms_accepted_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'submission_method' => $this->faker->randomElement(['web', 'mobile', 'admin']),
            'is_complete' => true,
            'completion_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'verification_status' => $this->faker->randomElement(['pending', 'verified', 'rejected']),
            'verification_notes' => $this->faker->optional()->paragraph(),
            'verification_date' => $this->faker->optional()->dateTimeBetween('-6 months', 'now'),
            'verifier_id' => $this->faker->optional()->numberBetween(1, 10),
            'priority_score' => $this->faker->optional()->numberBetween(1, 100),
            'ranking_position' => $this->faker->optional()->numberBetween(1, 50),
            'waitlist_position' => $this->faker->optional()->numberBetween(1, 20),
            'is_featured' => $this->faker->boolean(20),
            'featured_reason' => $this->faker->optional()->sentence(),
            'featured_date' => $this->faker->optional()->dateTimeBetween('-3 months', 'now'),
            'additional_notes' => $this->faker->optional()->paragraph(),
            'follow_up_date' => $this->faker->optional()->dateTimeBetween('now', '+3 months'),
            'follow_up_notes' => $this->faker->optional()->paragraph(),
            'is_archived' => false,
            'archived_at' => null,
            'archived_reason' => null,
            'archived_by' => null,
            'custom_fields' => json_encode([
                'field1' => $this->faker->word(),
                'field2' => $this->faker->numberBetween(1, 100),
                'field3' => $this->faker->boolean()
            ]),
            'metadata' => json_encode([
                'source' => $this->faker->randomElement(['website', 'mobile_app', 'referral']),
                'campaign' => $this->faker->optional()->word(),
                'utm_source' => $this->faker->optional()->word(),
                'utm_medium' => $this->faker->optional()->word(),
                'utm_campaign' => $this->faker->optional()->word()
            ])
        ];
    }

    /**
     * Indicate that the application is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'review_date' => null,
            'reviewer_id' => null,
            'award_amount' => null,
            'award_date' => null,
        ]);
    }

    /**
     * Indicate that the application is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'review_date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'reviewer_id' => $this->faker->numberBetween(1, 10),
            'award_amount' => $this->faker->numberBetween(50000, 200000),
            'award_date' => $this->faker->dateTimeBetween('-2 months', 'now'),
        ]);
    }

    /**
     * Indicate that the application is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'review_date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'reviewer_id' => $this->faker->numberBetween(1, 10),
            'reviewer_notes' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Indicate that the application is under review.
     */
    public function underReview(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'under_review',
            'reviewer_notes' => $this->faker->paragraph(),
        ]);
    }
} 