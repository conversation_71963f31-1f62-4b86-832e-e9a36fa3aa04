<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Authentication",
 *     description="API Endpoints for Authentication"
 * )
 */
class AuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/register",
     *     summary="Register a new user",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"first_name", "last_name", "email", "password", "password_confirmation"},
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password123"),
     *             @OA\Property(property="password_confirmation", type="string", format="password", example="password123"),
     *             @OA\Property(property="phone_number", type="string", example="+234123456789"),
     *             @OA\Property(property="address", type="string", example="123 Main Street"),
     *             @OA\Property(property="date_of_birth", type="string", format="date", example="1990-01-15"),
     *             @OA\Property(property="city", type="string", example="Lagos"),
     *             @OA\Property(property="state", type="string", example="Lagos State"),
     *             @OA\Property(property="country", type="string", example="Nigeria"),
     *             @OA\Property(property="user_type", type="string", enum={"student", "volunteer", "partner"}, example="student"),
     *             @OA\Property(property="additional_data", type="object", example={"institution": "University of Lagos"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User registered successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Registration successful"),
     *             @OA\Property(property="user", type="object"),
     *             @OA\Property(property="token", type="string", example="1|abc123...")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Validation failed"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function register(Request $request)
    {
        $validationRules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'user_type' => 'nullable|string|in:student,volunteer,partner'
        ];

        // Add partner-specific validation rules
        if ($request->user_type === 'partner') {
            $validationRules = array_merge($validationRules, [
                'organization_name' => 'required|string|max:255',
                'organization_type' => 'required|string|in:primary_school,secondary_school,university,ngo,government,corporate',
                'position' => 'required|string|max:100',
                'website' => 'nullable|url|max:255',
                'registration_number' => 'nullable|string|max:100',
                'facilities' => 'nullable|string|max:1000',
                'programs_offered' => 'nullable|string|max:1000',
            ]);
        }

        $validator = Validator::make($request->all(), $validationRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if email already exists (additional safety check)
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            return response()->json([
                'success' => false,
                'message' => 'An account with this email already exists. Please login or use a different email.'
            ], 422);
        }

        try {
            // Create the user
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone_number' => $request->phone_number,
                'address' => $request->address,
                'date_of_birth' => $request->date_of_birth,
                'city' => $request->city,
                'state' => $request->state,
                'country' => $request->country ?? 'Nigeria',
                'email_verified_at' => now(), // Auto-verify for development
                'role' => 'user' // Default role
            ]);

            // Handle user type specific registration
            $userType = $request->user_type ?? 'user';
            $additionalData = $request->additional_data ?? [];

            if ($request->user_type && $request->additional_data) {
                // Store user type preference
                $preferences = [
                    'user_type' => $userType,
                    'registration_completed' => true,
                    'additional_data' => $additionalData
                ];

                $user->update(['preferences' => json_encode($preferences)]);

                // Handle specific user type registrations
                if ($userType === 'volunteer') {
                    // Create volunteer application
                    $this->createVolunteerApplication($user, $additionalData);
                } elseif ($userType === 'student') {
                    // Store student specific data in preferences
                    $this->handleStudentRegistration($user, $additionalData);
                } elseif ($userType === 'partner') {
                    // Store partner specific data in preferences
                    $this->handlePartnerRegistration($user, $additionalData);
                }
            }

            // Don't auto-login, return success message for redirect to login
            return response()->json([
                'success' => true,
                'message' => 'Registration successful! Please login to access your dashboard.',
                'data' => [
                    'user_type' => $userType,
                    'redirect_to_login' => true
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Create volunteer application
     */
    private function createVolunteerApplication($user, $additionalData)
    {
        // This would create a volunteer application record
        // For now, we'll store it in user preferences
        // You can create a proper Volunteer model later
        $volunteerData = [
            'skills' => $additionalData['skills'] ?? '',
            'availability' => $additionalData['availability'] ?? '',
            'experience' => $additionalData['experience'] ?? '',
            'motivation' => $additionalData['motivation'] ?? '',
            'application_status' => 'pending',
            'applied_at' => now()->toISOString()
        ];

        $preferences = json_decode($user->preferences, true) ?? [];
        $preferences['volunteer_data'] = $volunteerData;
        $user->update(['preferences' => json_encode($preferences)]);
    }

    /**
     * Handle student registration
     */
    private function handleStudentRegistration($user, $additionalData)
    {
        $studentData = [
            'institution' => $additionalData['institution'] ?? '',
            'course' => $additionalData['course'] ?? '',
            'year_of_study' => $additionalData['year_of_study'] ?? '',
            'student_id' => $additionalData['student_id'] ?? '',
            'registration_completed' => true
        ];

        $preferences = json_decode($user->preferences, true) ?? [];
        $preferences['student_data'] = $studentData;
        $user->update(['preferences' => json_encode($preferences)]);
    }

    /**
     * Handle partner registration
     */
    private function handlePartnerRegistration($user, $additionalData)
    {
        // Set user role to partner_organization
        $user->update(['role' => 'partner_organization']);

        // Create PartnerOrganization record
        $partnerOrganization = \App\Models\PartnerOrganization::create([
            'name' => $additionalData['organization_name'] ?? '',
            'type' => $additionalData['organization_type'] ?? 'primary_school',
            'contact_person' => $user->full_name,
            'contact_email' => $user->email,
            'contact_phone' => $user->phone_number ?? '',
            'address' => $user->address ?? '',
            'city' => $user->city ?? '',
            'state' => $user->state ?? '',
            'country' => $user->country ?? 'Nigeria',
            'website' => $additionalData['website'] ?? '',
            'registration_number' => $additionalData['registration_number'] ?? '',
            'facilities' => $additionalData['facilities'] ?? '',
            'programs_offered' => $additionalData['programs_offered'] ?? '',
            'verification_status' => 'pending',
            'partnership_status' => 'pending',
            'created_by' => $user->id,
        ]);

        // Link user to partner organization
        $user->update([
            'partner_organization_id' => $partnerOrganization->id,
            'institutional_role' => $additionalData['position'] ?? 'admin',
            'institutional_verification_status' => 'pending'
        ]);

        // Store additional data in preferences for backward compatibility
        $partnerData = [
            'organization_name' => $additionalData['organization_name'] ?? '',
            'organization_type' => $additionalData['organization_type'] ?? '',
            'position' => $additionalData['position'] ?? '',
            'website' => $additionalData['website'] ?? '',
            'registration_completed' => true
        ];

        $preferences = json_decode($user->preferences, true) ?? [];
        $preferences['partner_data'] = $partnerData;
        $user->update(['preferences' => json_encode($preferences)]);
    }

    /**
     * @OA\Post(
     *     path="/api/auth/login",
     *     summary="User login",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email","password"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password"),
     *             @OA\Property(property="remember_me", type="boolean", example=false)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Login successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Login successful"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        if ($user->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Account is suspended. Please contact support.'
            ], 403);
        }

        // Update last login
        $user->update(['last_login_at' => now()]);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/auth/logout",
     *     summary="User logout",
     *     tags={"Authentication"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Logout successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Logout successful")
     *         )
     *     )
     * )
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/auth/forgot-password",
     *     summary="Send password reset link",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Password reset link sent",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Password reset link sent to your email")
     *         )
     *     )
     * )
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $status = Password::sendResetLink(
            $request->only('email')
        );

        if ($status === Password::RESET_LINK_SENT) {
            return response()->json([
                'success' => true,
                'message' => 'Password reset link sent to your email'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Unable to send password reset link'
            ], 400);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/auth/reset-password",
     *     summary="Reset password",
     *     tags={"Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"token","email","password","password_confirmation"},
     *             @OA\Property(property="token", type="string", example="reset_token"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="newpassword"),
     *             @OA\Property(property="password_confirmation", type="string", format="password", example="newpassword")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Password reset successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Password reset successful")
     *         )
     *     )
     * )
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([
                'success' => true,
                'message' => 'Password reset successful'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Invalid token or email'
            ], 400);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/auth/user",
     *     summary="Get authenticated user",
     *     tags={"Authentication"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="User data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     )
     * )
     */
    public function user(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $request->user()
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/auth/resend-verification",
     *     summary="Resend email verification",
     *     tags={"Authentication"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Verification email sent",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Verification email sent")
     *         )
     *     )
     * )
     */
    public function resendVerification(Request $request): JsonResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return response()->json([
                'success' => false,
                'message' => 'Email already verified'
            ], 400);
        }

        $request->user()->sendEmailVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'Verification email sent'
        ]);
    }

    /**
     * Check if email already exists
     */
    public function checkEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email format',
                'errors' => $validator->errors()
            ], 422);
        }

        $emailExists = User::where('email', $request->email)->exists();

        return response()->json([
            'exists' => $emailExists,
            'success' => true
        ]);
    }
}
